<Window x:Class="TestApp"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Test Material Design" Height="400" Width="600"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}"
        FlowDirection="RightToLeft">

    <Grid Margin="20">
        <StackPanel>
            <TextBlock Text="اختبار Material Design" 
                       Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,20"/>
            
            <materialDesign:Card Margin="10" Padding="20">
                <StackPanel>
                    <TextBlock Text="بطاقة اختبار" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}"/>
                    <TextBlock Text="هذا نص تجريبي لاختبار Material Design"
                               Margin="0,10,0,0"/>
                </StackPanel>
            </materialDesign:Card>
            
            <Button Content="زر اختبار"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Margin="10"
                    Click="TestButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
