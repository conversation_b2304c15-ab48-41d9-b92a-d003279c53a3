﻿#pragma checksum "..\..\..\..\Pages\SuppliersPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9826030DD62F9107FBFD5874106CEB8D0950F536"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// SuppliersPage
    /// </summary>
    public partial class SuppliersPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 218 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddNewSupplierButton;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SupplierStatementButton;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSuppliersText;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveSuppliersText;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalOutstandingText;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPaidText;
        
        #line default
        #line hidden
        
        
        #line 375 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SuppliersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 603 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 626 "..\..\..\..\Pages\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card EmptyStatePanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/supplierspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\SuppliersPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 2:
            this.AddNewSupplierButton = ((System.Windows.Controls.Button)(target));
            
            #line 222 "..\..\..\..\Pages\SuppliersPage.xaml"
            this.AddNewSupplierButton.Click += new System.Windows.RoutedEventHandler(this.AddNewSupplierButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SupplierStatementButton = ((System.Windows.Controls.Button)(target));
            
            #line 232 "..\..\..\..\Pages\SuppliersPage.xaml"
            this.SupplierStatementButton.Click += new System.Windows.RoutedEventHandler(this.SupplierStatementButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TotalSuppliersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ActiveSuppliersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TotalOutstandingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TotalPaidText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 384 "..\..\..\..\Pages\SuppliersPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SuppliersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 442 "..\..\..\..\Pages\SuppliersPage.xaml"
            this.SuppliersDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SuppliersDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.LoadingPanel = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 14:
            this.EmptyStatePanel = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            System.Windows.EventSetter eventSetter;
            switch (connectionId)
            {
            case 1:
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.MouseEnterEvent;
            
            #line 23 "..\..\..\..\Pages\SuppliersPage.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseEventHandler(this.StatCard_MouseEnter);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.MouseLeaveEvent;
            
            #line 24 "..\..\..\..\Pages\SuppliersPage.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseEventHandler(this.StatCard_MouseLeave);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            break;
            case 10:
            
            #line 566 "..\..\..\..\Pages\SuppliersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditSupplierButton_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 576 "..\..\..\..\Pages\SuppliersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteSupplierButton_Click);
            
            #line default
            #line hidden
            break;
            case 12:
            
            #line 586 "..\..\..\..\Pages\SuppliersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewSupplierDetailsButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

