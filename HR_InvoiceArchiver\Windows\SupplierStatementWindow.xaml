<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="HR_InvoiceArchiver.Windows.SupplierStatementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="كشف حساب المورد - أرشيف الفواتير"
        Height="900" Width="1600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">

    <Window.Resources>
        <!-- Styles -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="SubHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#34495E"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#34495E"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="ValueStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="FontWeight" Value="Normal"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="20,0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2980B9"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#27AE60"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#229954"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="InfoButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#17A2B8"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#138496"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Background="#ECF0F1">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="10,10,10,5">
            <StackPanel>
                <TextBlock x:Name="HeaderTextBlock" Text="كشف حساب المورد" Style="{StaticResource HeaderStyle}"/>
                <TextBlock x:Name="SupplierNameTextBlock" Text="" 
                          Foreground="#7F8C8D" FontSize="14" FontWeight="SemiBold"/>
            </StackPanel>
        </Border>

        <!-- Supplier Info & Summary -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Supplier Information -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="معلومات المورد" Style="{StaticResource SubHeaderStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المورد:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="SupplierNameValueTextBlock" Style="{StaticResource ValueStyle}"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الشخص المسؤول:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="ContactPersonTextBlock" Style="{StaticResource ValueStyle}"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="الهاتف:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" x:Name="PhoneTextBlock" Style="{StaticResource ValueStyle}"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="البريد الإلكتروني:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" x:Name="EmailTextBlock" Style="{StaticResource ValueStyle}"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="الرقم الضريبي:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="4" Grid.Column="1" x:Name="TaxNumberTextBlock" Style="{StaticResource ValueStyle}"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Account Summary -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="ملخص الحساب" Style="{StaticResource SubHeaderStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="عدد الفواتير:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="TotalInvoicesTextBlock" Style="{StaticResource ValueStyle}"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="إجمالي المبلغ:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="TotalAmountTextBlock" Style="{StaticResource ValueStyle}"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="المبلغ المدفوع:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" x:Name="PaidAmountTextBlock" Style="{StaticResource ValueStyle}"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ المتبقي:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" x:Name="RemainingAmountTextBlock" Style="{StaticResource ValueStyle}"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="الفواتير المتأخرة:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="4" Grid.Column="1" x:Name="OverdueInvoicesTextBlock" Style="{StaticResource ValueStyle}"/>

                        <TextBlock Grid.Row="5" Grid.Column="0" Text="آخر فاتورة:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Grid.Row="5" Grid.Column="1" x:Name="LastInvoiceDateTextBlock" Style="{StaticResource ValueStyle}"/>
                    </Grid>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Main Content -->
        <TabControl Grid.Row="2" Margin="10" Background="Transparent" BorderThickness="0">
            <TabItem Header="الفواتير" FontSize="14" FontWeight="SemiBold">
                <Border Style="{StaticResource CardStyle}" Margin="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Invoices Header -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="فواتير المورد" Style="{StaticResource SubHeaderStyle}" Margin="0,0,20,0"/>
                            <TextBlock x:Name="InvoiceCountTextBlock" Text="0 فاتورة" 
                                      VerticalAlignment="Center" Foreground="#7F8C8D"/>
                        </StackPanel>

                        <!-- Invoices DataGrid -->
                        <DataGrid x:Name="InvoicesDataGrid" Grid.Row="1" 
                                 AutoGenerateColumns="False" 
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 IsReadOnly="True"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 SelectionMode="Single"
                                 AlternatingRowBackground="#F8F9FA"
                                 RowHeight="35"
                                 FontSize="12">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                                <DataGridTextColumn Header="تاريخ الفاتورة" Binding="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}" Width="100"/>
                                <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat=yyyy/MM/dd}" Width="100"/>
                                <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="100"/>
                                <DataGridTextColumn Header="المدفوع" Binding="{Binding PaidAmount, StringFormat=N2}" Width="100"/>
                                <DataGridTextColumn Header="المتبقي" Binding="{Binding RemainingAmount, StringFormat=N2}" Width="100"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80"/>
                                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>
            </TabItem>

            <TabItem Header="المدفوعات" FontSize="14" FontWeight="SemiBold">
                <Border Style="{StaticResource CardStyle}" Margin="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Payments Header -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="مدفوعات المورد" Style="{StaticResource SubHeaderStyle}" Margin="0,0,20,0"/>
                            <TextBlock x:Name="PaymentCountTextBlock" Text="0 مدفوعة" 
                                      VerticalAlignment="Center" Foreground="#7F8C8D"/>
                        </StackPanel>

                        <!-- Payments DataGrid -->
                        <DataGrid x:Name="PaymentsDataGrid" Grid.Row="1" 
                                 AutoGenerateColumns="False" 
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 IsReadOnly="True"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 SelectionMode="Single"
                                 AlternatingRowBackground="#F8F9FA"
                                 RowHeight="35"
                                 FontSize="12">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم الوصل" Binding="{Binding ReceiptNumber}" Width="120"/>
                                <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding Invoice.InvoiceNumber}" Width="120"/>
                                <DataGridTextColumn Header="تاريخ الدفع" Binding="{Binding PaymentDate, StringFormat=yyyy/MM/dd}" Width="100"/>
                                <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="100"/>
                                <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding MethodText}" Width="100"/>
                                <DataGridTextColumn Header="المرجع" Binding="{Binding ReferenceNumber}" Width="120"/>
                                <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>
            </TabItem>
        </TabControl>

        <!-- Action Buttons -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}" Margin="10,5,10,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="PrintButton" Content="طباعة كشف الحساب" Style="{StaticResource PrimaryButtonStyle}" 
                       Margin="0,0,15,0" Click="PrintButton_Click"/>
                <Button x:Name="ExportButton" Content="تصدير إلى Excel" Style="{StaticResource SuccessButtonStyle}" 
                       Margin="0,0,15,0" Click="ExportButton_Click"/>
                <Button x:Name="RefreshButton" Content="تحديث البيانات" Style="{StaticResource InfoButtonStyle}" 
                       Click="RefreshButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
