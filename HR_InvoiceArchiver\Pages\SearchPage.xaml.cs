using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Utils;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;
using System.Globalization;

namespace HR_InvoiceArchiver.Pages
{
    public partial class SearchPage : UserControl, INavigationAware
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;
        private DispatcherTimer _searchTimer;

        public ObservableCollection<Invoice> SearchResults { get; set; } = new();
        public ObservableCollection<Supplier> Suppliers { get; set; } = new();

        // Statistics Properties
        public int TotalResults { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }

        public SearchPage(
            IInvoiceService invoiceService,
            ISupplierService supplierService,
            IToastService toastService,
            INavigationService navigationService)
        {
            InitializeComponent();
            _invoiceService = invoiceService;
            _supplierService = supplierService;
            _toastService = toastService;
            _navigationService = navigationService;

            ResultsDataGrid.ItemsSource = SearchResults;
            SupplierComboBox.ItemsSource = Suppliers;

            InitializeSearchTimer();
            Loaded += SearchPage_Loaded;
        }

        private void InitializeSearchTimer()
        {
            _searchTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(300)
            };
            _searchTimer.Tick += async (s, e) =>
            {
                _searchTimer.Stop();
                await PerformSearchAsync();
            };
        }

        private async void SearchPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSuppliersAsync();
            UpdateEmptyState();
        }

        public void OnNavigatedTo(object parameter)
        {
            // Refresh suppliers when navigating to this page
            Dispatcher.BeginInvoke(new Action(async () => await LoadSuppliersAsync()));
        }

        public void OnNavigatedFrom()
        {
            // Clean up timer
            _searchTimer?.Stop();
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersAsync();

                Dispatcher.Invoke(() =>
                {
                    Suppliers.Clear();
                    foreach (var supplier in suppliers)
                    {
                        Suppliers.Add(supplier);
                    }
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    _toastService.ShowError("خطأ في تحميل الموردين", ex.Message);
                });
            }
        }

        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformSearchAsync();
        }

        private async Task PerformSearchAsync()
        {
            try
            {
                ShowLoading(true);
                UpdateResultsInfo("جاري البحث...");

                // Add delay for better UX
                await Task.Delay(500);

                var invoices = await _invoiceService.GetAllInvoicesAsync();
                var filteredInvoices = invoices.AsQueryable();

                // Apply filters
                if (!string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
                {
                    filteredInvoices = filteredInvoices.Where(i =>
                        i.InvoiceNumber.Contains(InvoiceNumberTextBox.Text, StringComparison.OrdinalIgnoreCase));
                }

                if (SupplierComboBox.SelectedValue != null)
                {
                    var selectedSupplierId = (int)SupplierComboBox.SelectedValue;
                    filteredInvoices = filteredInvoices.Where(i => i.SupplierId == selectedSupplierId);
                }

                if (StatusComboBox.SelectedIndex > 0) // Skip "جميع الحالات"
                {
                    var selectedStatusText = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
                    var selectedStatus = selectedStatusText switch
                    {
                        "غير مدفوعة" => InvoiceStatus.Unpaid,
                        "مدفوعة جزئياً" => InvoiceStatus.PartiallyPaid,
                        "مدفوعة" => InvoiceStatus.Paid,
                        _ => InvoiceStatus.Unpaid
                    };
                    filteredInvoices = filteredInvoices.Where(i => i.Status == selectedStatus);
                }

                if (FromDatePicker.SelectedDate.HasValue)
                {
                    filteredInvoices = filteredInvoices.Where(i => i.InvoiceDate >= FromDatePicker.SelectedDate.Value);
                }

                if (ToDatePicker.SelectedDate.HasValue)
                {
                    filteredInvoices = filteredInvoices.Where(i => i.InvoiceDate <= ToDatePicker.SelectedDate.Value);
                }

                if (!string.IsNullOrWhiteSpace(AmountTextBox.Text) && decimal.TryParse(AmountTextBox.Text, out decimal amount))
                {
                    filteredInvoices = filteredInvoices.Where(i => i.Amount == amount);
                }
                {
                    filteredInvoices = filteredInvoices.Where(i => i.InvoiceDate <= ToDatePicker.SelectedDate.Value);
                }

                var results = filteredInvoices.OrderByDescending(i => i.InvoiceDate).ToList();

                Dispatcher.Invoke(() =>
                {
                    SearchResults.Clear();
                    foreach (var invoice in results)
                    {
                        SearchResults.Add(invoice);
                    }

                    UpdateStatistics();
                    UpdateResultsDisplay();
                    _toastService.ShowSuccess("تم البحث بنجاح", $"تم العثور على {results.Count} نتيجة");
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    _toastService.ShowError("خطأ في البحث", ex.Message);
                    UpdateResultsInfo("حدث خطأ أثناء البحث");
                });
            }
            finally
            {
                Dispatcher.Invoke(() => ShowLoading(false));
            }
        }

        private void ShowLoading(bool isLoading)
        {
            LoadingPanel.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            ResultsDataGrid.Visibility = isLoading ? Visibility.Collapsed : Visibility.Visible;
        }

        private void UpdateStatistics()
        {
            TotalResults = SearchResults.Count;
            TotalAmount = SearchResults.Sum(i => i.Amount);
            PaidAmount = SearchResults.Sum(i => i.PaidAmount);
            OutstandingAmount = TotalAmount - PaidAmount;

            // Update UI
            TotalResultsText.Text = TotalResults.ToString("N0");
            TotalAmountText.Text = TotalAmount.ToString("N0");
            PaidAmountText.Text = PaidAmount.ToString("N0");
            OutstandingAmountText.Text = OutstandingAmount.ToString("N0");
        }

        private void UpdateResultsDisplay()
        {
            var count = SearchResults.Count;
            ResultsCountText.Text = $"{count} نتيجة";

            EmptyStatePanel.Visibility = count == 0 ? Visibility.Visible : Visibility.Collapsed;
            ResultsDataGrid.Visibility = count == 0 ? Visibility.Collapsed : Visibility.Visible;

            if (count == 0)
            {
                UpdateResultsInfo("لا توجد نتائج مطابقة لمعايير البحث");
            }
            else
            {
                UpdateResultsInfo($"تم العثور على {count} نتيجة مطابقة");
            }
        }

        private void UpdateResultsInfo(string message)
        {
            ResultsInfoText.Text = message;
        }

        private void UpdateEmptyState()
        {
            EmptyStatePanel.Visibility = SearchResults.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            ResultsDataGrid.Visibility = SearchResults.Count == 0 ? Visibility.Collapsed : Visibility.Visible;
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            // Clear all search fields
            InvoiceNumberTextBox.Clear();
            SupplierComboBox.SelectedIndex = -1;
            StatusComboBox.SelectedIndex = 0;
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            AmountTextBox.Clear();

            // Clear results
            SearchResults.Clear();

            // Reset statistics
            TotalResults = 0;
            TotalAmount = 0;
            PaidAmount = 0;
            OutstandingAmount = 0;

            UpdateStatistics();
            UpdateResultsDisplay();
            UpdateEmptyState();

            _toastService.ShowInfo("تم مسح البحث", "تم مسح جميع معايير البحث والنتائج");
        }

        // Text change handlers for debounced search
        private void InvoiceNumberTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _searchTimer.Stop();
            _searchTimer.Start();
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _searchTimer.Stop();
            _searchTimer.Start();
        }

        private void SupplierComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            }
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            }
        }

        private void FromDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            }
        }

        private void ToDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            }
        }

        private async void ExportExcelButton_Click(object sender, RoutedEventArgs e)
        {
            if (SearchResults.Count == 0)
            {
                _toastService.ShowWarning("لا توجد بيانات للتصدير", "قم بالبحث أولاً للحصول على نتائج");
                return;
            }

            try
            {
                await ExportHelper.ExportInvoicesToExcelAsync(SearchResults.ToList(), "نتائج البحث");
                _toastService.ShowSuccess("تم التصدير بنجاح", $"تم تصدير {SearchResults.Count} فاتورة إلى Excel");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التصدير", ex.Message);
            }
        }

        private async void ExportPdfButton_Click(object sender, RoutedEventArgs e)
        {
            if (SearchResults.Count == 0)
            {
                _toastService.ShowWarning("لا توجد بيانات للتصدير", "قم بالبحث أولاً للحصول على نتائج");
                return;
            }

            try
            {
                await ExportHelper.ExportInvoicesToPdfAsync(SearchResults.ToList(), "نتائج البحث");
                _toastService.ShowSuccess("تم التصدير بنجاح", $"تم تصدير {SearchResults.Count} فاتورة إلى PDF");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التصدير", ex.Message);
            }
        }

        // DataGrid row double-click handler
        private void ResultsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (ResultsDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                // Navigate to invoices page with selected invoice
                _navigationService.NavigateTo(typeof(InvoicesPage), selectedInvoice.Id);
            }
        }
    }
}
