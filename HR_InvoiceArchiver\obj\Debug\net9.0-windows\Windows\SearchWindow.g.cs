﻿#pragma checksum "..\..\..\..\Windows\SearchWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "067FC9EBE181BF6AB4EF500A1FC90B1BCD9255DF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Windows {
    
    
    /// <summary>
    /// SearchWindow
    /// </summary>
    public partial class SearchWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoicesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OutstandingAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SupplierComboBox;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DueFromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DueToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox OverdueOnlyCheckBox;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResultCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ResultsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FirstPageButton;
        
        #line default
        #line hidden
        
        
        #line 387 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevPageButton;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 394 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextPageButton;
        
        #line default
        #line hidden
        
        
        #line 398 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LastPageButton;
        
        #line default
        #line hidden
        
        
        #line 426 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.PieChart StatusPieChart;
        
        #line default
        #line hidden
        
        
        #line 438 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NoDataPanel;
        
        #line default
        #line hidden
        
        
        #line 459 "..\..\..\..\Windows\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/windows/searchwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\SearchWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\Windows\SearchWindow.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 63 "..\..\..\..\Windows\SearchWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TotalInvoicesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TotalAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PaidAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.OutstandingAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.InvoiceNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.SupplierComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 10:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 11:
            this.DueFromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 12:
            this.DueToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 13:
            this.MinAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.MaxAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.OverdueOnlyCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 292 "..\..\..\..\Windows\SearchWindow.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 300 "..\..\..\..\Windows\SearchWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ResultCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ResultsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 351 "..\..\..\..\Windows\SearchWindow.xaml"
            this.ResultsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ResultsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 21:
            this.FirstPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 384 "..\..\..\..\Windows\SearchWindow.xaml"
            this.FirstPageButton.Click += new System.Windows.RoutedEventHandler(this.FirstPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.PrevPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 388 "..\..\..\..\Windows\SearchWindow.xaml"
            this.PrevPageButton.Click += new System.Windows.RoutedEventHandler(this.PrevPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.PageInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.NextPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 395 "..\..\..\..\Windows\SearchWindow.xaml"
            this.NextPageButton.Click += new System.Windows.RoutedEventHandler(this.NextPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.LastPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 399 "..\..\..\..\Windows\SearchWindow.xaml"
            this.LastPageButton.Click += new System.Windows.RoutedEventHandler(this.LastPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.StatusPieChart = ((LiveCharts.Wpf.PieChart)(target));
            return;
            case 27:
            this.NoDataPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 28:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

