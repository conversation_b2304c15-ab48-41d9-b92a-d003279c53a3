using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Services
{
    public interface ISupplierService
    {
        Task<IEnumerable<Supplier>> GetAllSuppliersAsync();
        Task<Supplier?> GetSupplierByIdAsync(int id);
        Task<Supplier?> GetSupplierByNameAsync(string name);
        Task<Supplier> CreateSupplierAsync(Supplier supplier);
        Task<Supplier> UpdateSupplierAsync(Supplier supplier);
        Task<bool> DeleteSupplierAsync(int id);
        Task<bool> SupplierExistsAsync(int id);
        Task<bool> SupplierNameExistsAsync(string name, int? excludeId = null);
        Task<IEnumerable<Supplier>> SearchSuppliersAsync(string searchTerm);
        Task<HR_InvoiceArchiver.Services.SupplierStatistics> GetSupplierStatisticsAsync(int supplierId);
        Task<HR_InvoiceArchiver.Services.DashboardStatistics> GetDashboardStatisticsAsync();
        Task<bool> ValidateSupplierAsync(Supplier supplier);
    }



    public class SupplierService : ISupplierService
    {
        private readonly ISupplierRepository _supplierRepository;
        private readonly IInvoiceRepository _invoiceRepository;
        private readonly IPaymentRepository _paymentRepository;

        public SupplierService(
            ISupplierRepository supplierRepository,
            IInvoiceRepository invoiceRepository,
            IPaymentRepository paymentRepository)
        {
            _supplierRepository = supplierRepository;
            _invoiceRepository = invoiceRepository;
            _paymentRepository = paymentRepository;
        }

        public async Task<IEnumerable<Supplier>> GetAllSuppliersAsync()
        {
            return await _supplierRepository.GetAllAsync();
        }

        public async Task<Supplier?> GetSupplierByIdAsync(int id)
        {
            return await _supplierRepository.GetByIdAsync(id);
        }

        public async Task<Supplier?> GetSupplierByNameAsync(string name)
        {
            return await _supplierRepository.GetByNameAsync(name);
        }

        public async Task<Supplier> CreateSupplierAsync(Supplier supplier)
        {
            if (!await ValidateSupplierAsync(supplier))
                throw new ArgumentException("بيانات المورد غير صحيحة");

            if (await _supplierRepository.ExistsByNameAsync(supplier.Name))
                throw new ArgumentException("يوجد مورد بنفس الاسم مسبقاً");

            return await _supplierRepository.AddAsync(supplier);
        }

        public async Task<Supplier> UpdateSupplierAsync(Supplier supplier)
        {
            if (!await ValidateSupplierAsync(supplier))
                throw new ArgumentException("بيانات المورد غير صحيحة");

            if (!await _supplierRepository.ExistsAsync(supplier.Id))
                throw new ArgumentException("المورد غير موجود");

            if (await _supplierRepository.ExistsByNameAsync(supplier.Name, supplier.Id))
                throw new ArgumentException("يوجد مورد آخر بنفس الاسم");

            return await _supplierRepository.UpdateAsync(supplier);
        }

        public async Task<bool> DeleteSupplierAsync(int id)
        {
            var supplier = await _supplierRepository.GetByIdAsync(id);
            if (supplier == null)
                return false;

            // Check if supplier has invoices
            if (supplier.Invoices.Any())
                throw new InvalidOperationException("لا يمكن حذف المورد لأنه يحتوي على فواتير");

            return await _supplierRepository.DeleteAsync(id);
        }

        public async Task<bool> SupplierExistsAsync(int id)
        {
            return await _supplierRepository.ExistsAsync(id);
        }

        public async Task<bool> SupplierNameExistsAsync(string name, int? excludeId = null)
        {
            return await _supplierRepository.ExistsByNameAsync(name, excludeId);
        }

        public async Task<IEnumerable<Supplier>> SearchSuppliersAsync(string searchTerm)
        {
            return await _supplierRepository.SearchAsync(searchTerm);
        }

        public async Task<SupplierStatistics> GetSupplierStatisticsAsync(int supplierId)
        {
            var supplier = await _supplierRepository.GetByIdAsync(supplierId);
            if (supplier == null)
                throw new ArgumentException("المورد غير موجود");

            var invoices = supplier.Invoices.ToList();
            var payments = invoices.SelectMany(i => i.Payments).ToList();

            return new HR_InvoiceArchiver.Services.SupplierStatistics
            {
                SupplierId = supplier.Id,
                SupplierName = supplier.Name,
                TotalInvoices = invoices.Count,
                PaidInvoices = invoices.Count(i => i.Status == InvoiceStatus.Paid),
                UnpaidInvoices = invoices.Count(i => i.Status == InvoiceStatus.Unpaid),
                PartiallyPaidInvoices = invoices.Count(i => i.Status == InvoiceStatus.PartiallyPaid),
                InvoiceCount = invoices.Count,
                TotalAmount = invoices.Sum(i => i.Amount),
                PaidAmount = invoices.Sum(i => i.PaidAmount),
                OutstandingAmount = invoices.Sum(i => i.RemainingAmount),
                PaymentRate = invoices.Count > 0 ? (double)invoices.Count(i => i.Status == InvoiceStatus.Paid) / invoices.Count * 100 : 0,
                AveragePaymentTime = TimeSpan.Zero, // يمكن حسابها لاحقاً
                LastInvoiceDate = invoices.OrderByDescending(i => i.InvoiceDate).FirstOrDefault()?.InvoiceDate ?? DateTime.MinValue,
                LastPaymentDate = payments.OrderByDescending(p => p.PaymentDate).FirstOrDefault()?.PaymentDate ?? DateTime.MinValue,
                IsActive = invoices.Any(i => i.InvoiceDate > DateTime.Now.AddMonths(-3)),
                RiskLevel = "منخفض"
            };
        }

        public async Task<HR_InvoiceArchiver.Services.DashboardStatistics> GetDashboardStatisticsAsync()
        {
            var totalSuppliers = await _supplierRepository.GetTotalCountAsync();
            var totalInvoices = await _invoiceRepository.GetTotalCountAsync();
            var totalInvoicesAmount = await _invoiceRepository.GetTotalAmountAsync();
            var totalPaidAmount = await _invoiceRepository.GetTotalPaidAmountAsync();
            var totalPayments = await _paymentRepository.GetTotalCountAsync();

            var paidInvoices = await _invoiceRepository.GetCountByStatusAsync(InvoiceStatus.Paid);
            var unpaidInvoices = await _invoiceRepository.GetCountByStatusAsync(InvoiceStatus.Unpaid);
            var partiallyPaidInvoices = await _invoiceRepository.GetCountByStatusAsync(InvoiceStatus.PartiallyPaid);

            var overdueInvoices = await _invoiceRepository.GetOverdueInvoicesAsync();
            var recentPayments = await _paymentRepository.GetRecentPaymentsAsync(5);

            // Get recent invoices (last 5)
            var allInvoices = await _invoiceRepository.GetAllAsync();
            var recentInvoices = allInvoices.OrderByDescending(i => i.CreatedDate).Take(5);

            return new HR_InvoiceArchiver.Services.DashboardStatistics
            {
                TotalSuppliers = totalSuppliers,
                TotalInvoices = totalInvoices,
                PaidInvoices = paidInvoices,
                UnpaidInvoices = unpaidInvoices,
                PartiallyPaidInvoices = partiallyPaidInvoices,
                OverdueInvoices = overdueInvoices.Count(),
                TotalAmount = totalInvoicesAmount,
                PaidAmount = totalPaidAmount,
                OutstandingAmount = totalInvoicesAmount - totalPaidAmount,
                MonthlyGrowthRate = 0, // يمكن حسابها لاحقاً
                YearlyGrowthRate = 0, // يمكن حسابها لاحقاً
                PaymentRate = totalInvoices > 0 ? (double)paidInvoices / totalInvoices * 100 : 0,
                AverageInvoiceAmount = totalInvoices > 0 ? totalInvoicesAmount / totalInvoices : 0
            };
        }

        public async Task<bool> ValidateSupplierAsync(Supplier supplier)
        {
            if (string.IsNullOrWhiteSpace(supplier.Name))
                return false;

            if (supplier.Name.Length > 200)
                return false;

            if (!string.IsNullOrEmpty(supplier.Email) && !IsValidEmail(supplier.Email))
                return false;

            return await Task.FromResult(true);
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
