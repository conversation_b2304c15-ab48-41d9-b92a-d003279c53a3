using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Data.Repositories
{
    public interface ISupplierRepository
    {
        Task<IEnumerable<Supplier>> GetAllAsync();
        Task<Supplier?> GetByIdAsync(int id);
        Task<Supplier?> GetByNameAsync(string name);
        Task<Supplier> AddAsync(Supplier supplier);
        Task<Supplier> UpdateAsync(Supplier supplier);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> ExistsByNameAsync(string name, int? excludeId = null);
        Task<IEnumerable<Supplier>> SearchAsync(string searchTerm);
        Task<int> GetTotalCountAsync();
    }

    public class SupplierRepository : ISupplierRepository
    {
        private readonly DatabaseContext _context;

        public SupplierRepository(DatabaseContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Supplier>> GetAllAsync()
        {
            return await _context.Suppliers
                .Where(s => s.IsActive)
                .Include(s => s.Invoices.Where(i => i.IsActive))
                .ThenInclude(i => i.Payments.Where(p => p.IsActive))
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Supplier?> GetByIdAsync(int id)
        {
            return await _context.Suppliers
                .Include(s => s.Invoices.Where(i => i.IsActive))
                .ThenInclude(i => i.Payments.Where(p => p.IsActive))
                .FirstOrDefaultAsync(s => s.Id == id && s.IsActive);
        }

        public async Task<Supplier?> GetByNameAsync(string name)
        {
            return await _context.Suppliers
                .Include(s => s.Invoices.Where(i => i.IsActive))
                .ThenInclude(i => i.Payments.Where(p => p.IsActive))
                .FirstOrDefaultAsync(s => s.Name.ToLower() == name.ToLower() && s.IsActive);
        }

        public async Task<Supplier> AddAsync(Supplier supplier)
        {
            supplier.CreatedDate = DateTime.Now;
            supplier.IsActive = true;
            
            _context.Suppliers.Add(supplier);
            await _context.SaveChangesAsync();
            return supplier;
        }

        public async Task<Supplier> UpdateAsync(Supplier supplier)
        {
            supplier.UpdatedDate = DateTime.Now;
            
            _context.Entry(supplier).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return supplier;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var supplier = await _context.Suppliers.FindAsync(id);
            if (supplier == null) return false;

            // Soft delete
            supplier.IsActive = false;
            supplier.UpdatedDate = DateTime.Now;
            
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Suppliers.AnyAsync(s => s.Id == id && s.IsActive);
        }

        public async Task<bool> ExistsByNameAsync(string name, int? excludeId = null)
        {
            var query = _context.Suppliers.Where(s => s.Name.ToLower() == name.ToLower() && s.IsActive);

            if (excludeId.HasValue)
                query = query.Where(s => s.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<IEnumerable<Supplier>> SearchAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllAsync();

            searchTerm = searchTerm.Trim().ToLower();

            return await _context.Suppliers
                .Where(s => s.IsActive && 
                           (s.Name.ToLower().Contains(searchTerm) ||
                            (s.ContactPerson != null && s.ContactPerson.ToLower().Contains(searchTerm)) ||
                            (s.Phone != null && s.Phone.Contains(searchTerm)) ||
                            (s.Email != null && s.Email.ToLower().Contains(searchTerm))))
                .Include(s => s.Invoices.Where(i => i.IsActive))
                .ThenInclude(i => i.Payments.Where(p => p.IsActive))
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<int> GetTotalCountAsync()
        {
            return await _context.Suppliers.CountAsync(s => s.IsActive);
        }
    }
}
