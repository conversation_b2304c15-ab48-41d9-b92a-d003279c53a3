using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace HR_InvoiceArchiver.Pages
{
    public partial class ReportsPage : UserControl
    {
        private readonly IInvoiceService? _invoiceService;
        private readonly IPaymentService? _paymentService;
        private readonly ISupplierService? _supplierService;
        private readonly IToastService? _toastService;

        private List<Invoice> _allInvoices = new();
        private List<Payment> _allPayments = new();
        private List<Supplier> _allSuppliers = new();
        private ObservableCollection<object> _currentReportData = new();
        private DispatcherTimer _searchTimer;
        private string _currentReportType = "";

        // Constructor for DI
        public ReportsPage(
            IInvoiceService invoiceService,
            IPaymentService paymentService,
            ISupplierService supplierService,
            IToastService toastService)
        {
            InitializeComponent();
            _invoiceService = invoiceService;
            _paymentService = paymentService;
            _supplierService = supplierService;
            _toastService = toastService;
            InitializeSearchTimer();
        }

        // Parameterless constructor for Activator fallback
        public ReportsPage()
        {
            InitializeComponent();

            try
            {
                _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
                _paymentService = App.ServiceProvider.GetRequiredService<IPaymentService>();
                _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
                _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
                InitializeSearchTimer();
            }
            catch (Exception ex)
            {
                // Log error if needed
                System.Diagnostics.Debug.WriteLine($"Error resolving services: {ex.Message}");
            }
        }

        private void InitializeSearchTimer()
        {
            _searchTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(300)
            };
            _searchTimer.Tick += SearchTimer_Tick;
        }

        private async void LoadDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {

                StatusText.Text = "جاري تحميل البيانات...";
                LoadDataButton.IsEnabled = false;

                if (_invoiceService == null || _paymentService == null)
                {
                    StatusText.Text = "خطأ: الخدمات غير متوفرة";
                    return;
                }

                var invoices = await _invoiceService.GetAllInvoicesAsync();
                var payments = await _paymentService.GetAllPaymentsAsync();

                var totalInvoices = invoices.Count();
                var totalAmount = invoices.Sum(i => i.Amount);
                var totalPayments = payments.Sum(p => p.Amount);
                var outstandingAmount = totalAmount - totalPayments;

                TotalInvoicesText.Text = totalInvoices.ToString("N0");
                TotalAmountText.Text = totalAmount.ToString("N0");
                TotalPaymentsText.Text = totalPayments.ToString("N0");
                OutstandingAmountText.Text = outstandingAmount.ToString("N0");

                StatusText.Text = $"تم تحميل البيانات بنجاح - آخر تحديث: {DateTime.Now:HH:mm:ss}";
                
                _toastService?.ShowSuccess("نجح التحميل", "تم تحميل البيانات بنجاح");
                

            }
            catch (Exception ex)
            {

                StatusText.Text = $"خطأ في تحميل البيانات: {ex.Message}";
                _toastService?.ShowError("خطأ", "فشل في تحميل البيانات");
            }
            finally
            {
                LoadDataButton.IsEnabled = true;
            }
        }

        private async void InvoicesReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await EnsureDataLoaded();
                ResetButtonStyles();
                SetActiveButtonStyle(InvoicesReportButton,
                    System.Windows.Media.Color.FromRgb(33, 150, 243), // #2196F3
                    System.Windows.Media.Color.FromRgb(25, 118, 210)); // #1976D2

                _currentReportType = "invoices";
                ReportTitleText.Text = "📄 تقرير الفواتير";
                ReportDateText.Text = $"إجمالي {_allInvoices.Count} فاتورة";

                var invoiceData = _allInvoices.Select(i => new
                {
                    رقم_الفاتورة = i.InvoiceNumber,
                    المورد = _allSuppliers.FirstOrDefault(s => s.Id == i.SupplierId)?.Name ?? "غير محدد",
                    المبلغ = i.Amount.ToString("N0", System.Globalization.CultureInfo.InvariantCulture),
                    تاريخ_الفاتورة = i.InvoiceDate.ToString("yyyy/MM/dd"),
                    تاريخ_الاستحقاق = i.DueDate?.ToString("yyyy/MM/dd") ?? "غير محدد",
                    الحالة = GetStatusText(i.Status),
                    الوصف = i.Description
                }).ToList();

                SetupInvoicesDataGrid();
                _currentReportData = new ObservableCollection<object>(invoiceData);
                ReportDataGrid.ItemsSource = _currentReportData;
                ShowReportArea();
                ExportButton.IsEnabled = true;
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل تقرير الفواتير: {ex.Message}");
            }
        }

        private async void PaymentsReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await EnsureDataLoaded();
                ResetButtonStyles();
                SetActiveButtonStyle(PaymentsReportButton,
                    System.Windows.Media.Color.FromRgb(76, 175, 80), // #4CAF50
                    System.Windows.Media.Color.FromRgb(56, 142, 60)); // #388E3C

                _currentReportType = "payments";
                ReportTitleText.Text = "💳 تقرير المدفوعات";
                ReportDateText.Text = $"إجمالي {_allPayments.Count} دفعة";

                var paymentData = _allPayments.Select(p => new
                {
                    رقم_الإيصال = p.ReceiptNumber,
                    رقم_الفاتورة = _allInvoices.FirstOrDefault(i => i.Id == p.InvoiceId)?.InvoiceNumber ?? "غير محدد",
                    المورد = GetSupplierNameByPayment(p),
                    المبلغ = p.Amount.ToString("N0", System.Globalization.CultureInfo.InvariantCulture),
                    تاريخ_الدفع = p.PaymentDate.ToString("yyyy/MM/dd"),
                    طريقة_الدفع = p.MethodText,
                    التفاصيل = p.Details
                }).ToList();

                SetupPaymentsDataGrid();
                _currentReportData = new ObservableCollection<object>(paymentData);
                ReportDataGrid.ItemsSource = _currentReportData;
                ShowReportArea();
                ExportButton.IsEnabled = true;
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل تقرير المدفوعات: {ex.Message}");
            }
        }

        private async void SuppliersReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await EnsureDataLoaded();
                ResetButtonStyles();
                SetActiveButtonStyle(SuppliersReportButton,
                    System.Windows.Media.Color.FromRgb(255, 152, 0), // #FF9800
                    System.Windows.Media.Color.FromRgb(245, 124, 0)); // #F57C00

                _currentReportType = "suppliers";
                ReportTitleText.Text = "🏢 تقرير الموردين";
                ReportDateText.Text = $"إجمالي {_allSuppliers.Count} مورد";

                var supplierData = _allSuppliers.Select(s => new
                {
                    اسم_المورد = s.Name,
                    الهاتف = s.Phone,
                    العنوان = s.Address,
                    عدد_الفواتير = _allInvoices.Count(i => i.SupplierId == s.Id),
                    إجمالي_الفواتير = _allInvoices.Where(i => i.SupplierId == s.Id).Sum(i => i.Amount).ToString("N0", System.Globalization.CultureInfo.InvariantCulture),
                    إجمالي_المدفوعات = GetSupplierPayments(s.Id).ToString("N0", System.Globalization.CultureInfo.InvariantCulture),
                    الرصيد_المستحق = (_allInvoices.Where(i => i.SupplierId == s.Id).Sum(i => i.Amount) - GetSupplierPayments(s.Id)).ToString("N0", System.Globalization.CultureInfo.InvariantCulture)
                }).ToList();

                SetupSuppliersDataGrid();
                _currentReportData = new ObservableCollection<object>(supplierData);
                ReportDataGrid.ItemsSource = _currentReportData;
                ShowReportArea();
                ExportButton.IsEnabled = true;
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل تقرير الموردين: {ex.Message}");
            }
        }

        private async void MonthlyReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await EnsureDataLoaded();
                ResetButtonStyles();
                SetActiveButtonStyle(MonthlyReportButton,
                    System.Windows.Media.Color.FromRgb(156, 39, 176), // #9C27B0
                    System.Windows.Media.Color.FromRgb(123, 31, 162)); // #7B1FA2

                _currentReportType = "monthly";
                ReportTitleText.Text = "📊 التقرير الشهري";
                ReportDateText.Text = "تقرير شهري مفصل";

                var monthlyData = _allInvoices
                    .GroupBy(i => new { Year = i.InvoiceDate.Year, Month = i.InvoiceDate.Month })
                    .Select(g => new
                    {
                        الشهر = $"{g.Key.Year}/{g.Key.Month:00}",
                        عدد_الفواتير = g.Count(),
                        إجمالي_الفواتير = g.Sum(i => i.Amount).ToString("N0", System.Globalization.CultureInfo.InvariantCulture),
                        إجمالي_المدفوعات = GetMonthlyPayments(g.Key.Year, g.Key.Month).ToString("N0", System.Globalization.CultureInfo.InvariantCulture),
                        الرصيد_المستحق = (g.Sum(i => i.Amount) - GetMonthlyPayments(g.Key.Year, g.Key.Month)).ToString("N0", System.Globalization.CultureInfo.InvariantCulture),
                        متوسط_الفاتورة = (g.Sum(i => i.Amount) / g.Count()).ToString("N0", System.Globalization.CultureInfo.InvariantCulture)
                    })
                    .OrderByDescending(x => x.الشهر)
                    .ToList();

                SetupMonthlyDataGrid();
                _currentReportData = new ObservableCollection<object>(monthlyData);
                ReportDataGrid.ItemsSource = _currentReportData;
                ShowReportArea();
                ExportButton.IsEnabled = true;
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل التقرير الشهري: {ex.Message}");
            }
        }

        private async Task EnsureDataLoaded()
        {
            if (!_allInvoices.Any() || !_allPayments.Any() || !_allSuppliers.Any())
            {
                await LoadAllData();
            }
        }

        private async Task LoadAllData()
        {
            if (_invoiceService == null || _paymentService == null || _supplierService == null)
                return;

            var invoicesTask = _invoiceService.GetAllInvoicesAsync();
            var paymentsTask = _paymentService.GetAllPaymentsAsync();
            var suppliersTask = _supplierService.GetAllSuppliersAsync();

            await Task.WhenAll(invoicesTask, paymentsTask, suppliersTask);

            _allInvoices = invoicesTask.Result.ToList();
            _allPayments = paymentsTask.Result.ToList();
            _allSuppliers = suppliersTask.Result.ToList();

            UpdateStatistics();
        }

        private void UpdateStatistics()
        {
            var totalInvoices = _allInvoices.Count;
            var totalAmount = _allInvoices.Sum(i => i.Amount);
            var totalPayments = _allPayments.Sum(p => p.Amount);
            var outstandingAmount = totalAmount - totalPayments;

            TotalInvoicesText.Text = totalInvoices.ToString("N0", System.Globalization.CultureInfo.InvariantCulture);
            TotalAmountText.Text = totalAmount.ToString("N0", System.Globalization.CultureInfo.InvariantCulture);
            TotalPaymentsText.Text = totalPayments.ToString("N0", System.Globalization.CultureInfo.InvariantCulture);
            OutstandingAmountText.Text = outstandingAmount.ToString("N0", System.Globalization.CultureInfo.InvariantCulture);
        }

        private void ResetButtonStyles()
        {
            // Reset all buttons to transparent background
            InvoicesReportButton.Background = System.Windows.Media.Brushes.Transparent;
            PaymentsReportButton.Background = System.Windows.Media.Brushes.Transparent;
            SuppliersReportButton.Background = System.Windows.Media.Brushes.Transparent;
            MonthlyReportButton.Background = System.Windows.Media.Brushes.Transparent;

            // Reset border colors to default
            var invoicesBorder = (Border)InvoicesReportButton.Parent;
            var paymentsBorder = (Border)PaymentsReportButton.Parent;
            var suppliersBorder = (Border)SuppliersReportButton.Parent;
            var monthlyBorder = (Border)MonthlyReportButton.Parent;

            invoicesBorder.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(227, 242, 253)); // #E3F2FD
            paymentsBorder.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(232, 245, 232)); // #E8F5E8
            suppliersBorder.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 243, 224)); // #FFF3E0
            monthlyBorder.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(243, 229, 245)); // #F3E5F5
        }

        private void SetActiveButtonStyle(Button activeButton, System.Windows.Media.Color primaryColor, System.Windows.Media.Color secondaryColor)
        {
            var border = (Border)activeButton.Parent;

            // Create gradient background
            var gradientBrush = new System.Windows.Media.LinearGradientBrush();
            gradientBrush.StartPoint = new System.Windows.Point(0, 0);
            gradientBrush.EndPoint = new System.Windows.Point(1, 1);
            gradientBrush.GradientStops.Add(new System.Windows.Media.GradientStop(primaryColor, 0));
            gradientBrush.GradientStops.Add(new System.Windows.Media.GradientStop(secondaryColor, 1));

            activeButton.Background = gradientBrush;
            border.BorderBrush = new System.Windows.Media.SolidColorBrush(primaryColor);
            border.BorderThickness = new System.Windows.Thickness(3);

            // Add glow effect
            var dropShadow = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = primaryColor,
                Direction = 270,
                ShadowDepth = 0,
                BlurRadius = 15,
                Opacity = 0.6
            };
            border.Effect = dropShadow;
        }

        private void ShowReportArea()
        {
            EmptyStatePanel.Visibility = Visibility.Collapsed;
            ReportDataGrid.Visibility = Visibility.Visible;
        }

        private string GetStatusText(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Unpaid => "غير مدفوعة",
                InvoiceStatus.PartiallyPaid => "مدفوعة جزئياً",
                InvoiceStatus.Paid => "مدفوعة",
                InvoiceStatus.PaidWithDiscount => "مدفوعة بخصم",
                _ => "غير محدد"
            };
        }

        private string GetSupplierNameByPayment(Payment payment)
        {
            var invoice = _allInvoices.FirstOrDefault(i => i.Id == payment.InvoiceId);
            if (invoice != null)
            {
                var supplier = _allSuppliers.FirstOrDefault(s => s.Id == invoice.SupplierId);
                return supplier?.Name ?? "غير محدد";
            }
            return "غير محدد";
        }

        private decimal GetSupplierPayments(int supplierId)
        {
            var supplierInvoiceIds = _allInvoices.Where(i => i.SupplierId == supplierId).Select(i => i.Id);
            return _allPayments.Where(p => supplierInvoiceIds.Contains(p.InvoiceId)).Sum(p => p.Amount);
        }

        private decimal GetMonthlyPayments(int year, int month)
        {
            return _allPayments.Where(p => p.PaymentDate.Year == year && p.PaymentDate.Month == month).Sum(p => p.Amount);
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _searchTimer.Stop();
            _searchTimer.Start();

            ClearSearchButton.Visibility = string.IsNullOrEmpty(SearchTextBox.Text) ?
                Visibility.Collapsed : Visibility.Visible;
        }

        private void SearchTimer_Tick(object sender, EventArgs e)
        {
            _searchTimer.Stop();
            PerformSearch();
        }

        private void ClearSearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            PerformSearch();
        }

        private void PerformSearch()
        {
            if (ReportDataGrid.ItemsSource == null) return;

            var searchText = SearchTextBox.Text?.ToLower() ?? "";

            if (string.IsNullOrEmpty(searchText))
            {
                ReportDataGrid.ItemsSource = _currentReportData;
                return;
            }

            var filteredData = _currentReportData.Where(item =>
            {
                var properties = item.GetType().GetProperties();
                return properties.Any(prop =>
                {
                    var value = prop.GetValue(item)?.ToString()?.ToLower() ?? "";
                    return value.Contains(searchText);
                });
            }).ToList();

            ReportDataGrid.ItemsSource = new ObservableCollection<object>(filteredData);
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            if (ReportDataGrid.ItemsSource == null || !_currentReportData.Any())
            {
                _toastService?.ShowWarning("تحذير", "لا توجد بيانات للتصدير");
                return;
            }

            var saveFileDialog = new SaveFileDialog
            {
                Filter = "CSV Files (*.csv)|*.csv|HTML Files (*.html)|*.html",
                DefaultExt = "csv",
                FileName = $"تقرير_{_currentReportType}_{DateTime.Now:yyyyMMdd_HHmmss}"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    if (saveFileDialog.FilterIndex == 1) // CSV
                    {
                        ExportToCsv(saveFileDialog.FileName);
                    }
                    else // HTML
                    {
                        ExportToHtml(saveFileDialog.FileName);
                    }

                    _toastService?.ShowSuccess("نجح التصدير", $"تم تصدير التقرير إلى: {saveFileDialog.FileName}");
                }
                catch (Exception ex)
                {
                    _toastService?.ShowError("خطأ في التصدير", ex.Message);
                }
            }
        }

        private void ExportToCsv(string fileName)
        {
            var csv = new StringBuilder();

            if (_currentReportData.Any())
            {
                var firstItem = _currentReportData.First();
                var properties = firstItem.GetType().GetProperties();

                // Headers
                csv.AppendLine(string.Join(",", properties.Select(p => $"\"{p.Name}\"")));

                // Data
                foreach (var item in _currentReportData)
                {
                    var values = properties.Select(p => $"\"{p.GetValue(item) ?? ""}\"");
                    csv.AppendLine(string.Join(",", values));
                }
            }

            File.WriteAllText(fileName, csv.ToString(), Encoding.UTF8);
        }

        private void ExportToHtml(string fileName)
        {
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine($"<title>{ReportTitleText.Text}</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: Arial, sans-serif; direction: rtl; }");
            html.AppendLine("table { border-collapse: collapse; width: 100%; margin: 20px 0; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }");
            html.AppendLine("th { background-color: #2196F3; color: white; }");
            html.AppendLine("tr:nth-child(even) { background-color: #f2f2f2; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            html.AppendLine($"<h1>{ReportTitleText.Text}</h1>");
            html.AppendLine($"<p>تاريخ التصدير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}</p>");

            if (_currentReportData.Any())
            {
                var firstItem = _currentReportData.First();
                var properties = firstItem.GetType().GetProperties();

                html.AppendLine("<table>");
                html.AppendLine("<thead><tr>");
                foreach (var prop in properties)
                {
                    html.AppendLine($"<th>{prop.Name}</th>");
                }
                html.AppendLine("</tr></thead>");

                html.AppendLine("<tbody>");
                foreach (var item in _currentReportData)
                {
                    html.AppendLine("<tr>");
                    foreach (var prop in properties)
                    {
                        html.AppendLine($"<td>{prop.GetValue(item) ?? ""}</td>");
                    }
                    html.AppendLine("</tr>");
                }
                html.AppendLine("</tbody>");
                html.AppendLine("</table>");
            }

            html.AppendLine("</body>");
            html.AppendLine("</html>");

            File.WriteAllText(fileName, html.ToString(), Encoding.UTF8);
        }

        private void SetupInvoicesDataGrid()
        {
            ReportDataGrid.Columns.Clear();
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "رقم الفاتورة", Binding = new System.Windows.Data.Binding("رقم_الفاتورة"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "المورد", Binding = new System.Windows.Data.Binding("المورد"), Width = 150 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "المبلغ", Binding = new System.Windows.Data.Binding("المبلغ"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "تاريخ الفاتورة", Binding = new System.Windows.Data.Binding("تاريخ_الفاتورة"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "تاريخ الاستحقاق", Binding = new System.Windows.Data.Binding("تاريخ_الاستحقاق"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("الحالة"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "الوصف", Binding = new System.Windows.Data.Binding("الوصف"), Width = 200 });
        }

        private void SetupPaymentsDataGrid()
        {
            ReportDataGrid.Columns.Clear();
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "رقم الإيصال", Binding = new System.Windows.Data.Binding("رقم_الإيصال"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "رقم الفاتورة", Binding = new System.Windows.Data.Binding("رقم_الفاتورة"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "المورد", Binding = new System.Windows.Data.Binding("المورد"), Width = 150 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "المبلغ", Binding = new System.Windows.Data.Binding("المبلغ"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "تاريخ الدفع", Binding = new System.Windows.Data.Binding("تاريخ_الدفع"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "طريقة الدفع", Binding = new System.Windows.Data.Binding("طريقة_الدفع"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "التفاصيل", Binding = new System.Windows.Data.Binding("التفاصيل"), Width = 200 });
        }

        private void SetupSuppliersDataGrid()
        {
            ReportDataGrid.Columns.Clear();
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "اسم المورد", Binding = new System.Windows.Data.Binding("اسم_المورد"), Width = 150 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "الهاتف", Binding = new System.Windows.Data.Binding("الهاتف"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "العنوان", Binding = new System.Windows.Data.Binding("العنوان"), Width = 200 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "عدد الفواتير", Binding = new System.Windows.Data.Binding("عدد_الفواتير"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "إجمالي الفواتير", Binding = new System.Windows.Data.Binding("إجمالي_الفواتير"), Width = 120 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "إجمالي المدفوعات", Binding = new System.Windows.Data.Binding("إجمالي_المدفوعات"), Width = 120 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "الرصيد المستحق", Binding = new System.Windows.Data.Binding("الرصيد_المستحق"), Width = 120 });
        }

        private void SetupMonthlyDataGrid()
        {
            ReportDataGrid.Columns.Clear();
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "الشهر", Binding = new System.Windows.Data.Binding("الشهر"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "عدد الفواتير", Binding = new System.Windows.Data.Binding("عدد_الفواتير"), Width = 100 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "إجمالي الفواتير", Binding = new System.Windows.Data.Binding("إجمالي_الفواتير"), Width = 120 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "إجمالي المدفوعات", Binding = new System.Windows.Data.Binding("إجمالي_المدفوعات"), Width = 120 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "الرصيد المستحق", Binding = new System.Windows.Data.Binding("الرصيد_المستحق"), Width = 120 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "متوسط الفاتورة", Binding = new System.Windows.Data.Binding("متوسط_الفاتورة"), Width = 120 });
        }
    }
}
