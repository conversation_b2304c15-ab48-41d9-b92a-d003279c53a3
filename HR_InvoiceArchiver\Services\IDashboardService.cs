using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Services
{
    public interface IDashboardService
    {
        Task<DashboardStatistics> GetDashboardStatisticsAsync();
        Task<IEnumerable<MonthlyTrend>> GetMonthlyTrendsAsync(int months = 6);
        Task<IEnumerable<SupplierStatistics>> GetTopSuppliersAsync(int count = 10);
        Task<IEnumerable<PaymentTrend>> GetPaymentTrendsAsync(int months = 6);
        Task<CashFlowAnalysis> GetCashFlowAnalysisAsync();
        Task<IEnumerable<AlertItem>> GetDashboardAlertsAsync();
        Task<ExportResult> ExportDashboardDataAsync(ExportFormat format, ExportOptions options);
        Task<bool> MarkAlertAsReadAsync(int alertId);
        Task<bool> ClearAllAlertsAsync();
        Task<DashboardSettings> GetDashboardSettingsAsync();
        Task<bool> UpdateDashboardSettingsAsync(DashboardSettings settings);
    }

    public class DashboardStatistics
    {
        public int TotalInvoices { get; set; }
        public int PaidInvoices { get; set; }
        public int UnpaidInvoices { get; set; }
        public int PartiallyPaidInvoices { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int OverdueInvoices { get; set; }
        public int UnpaidCount { get; set; }
        public int PartiallyPaidCount { get; set; }
        public int PaidCount { get; set; }
        public double PaymentRate { get; set; }
        public DateTime LastUpdated { get; set; }
        public decimal AverageInvoiceAmount { get; set; }
        public decimal LargestInvoiceAmount { get; set; }
        public decimal SmallestInvoiceAmount { get; set; }
        public int TotalSuppliers { get; set; }
        public int ActiveSuppliers { get; set; }
        public TimeSpan AveragePaymentTime { get; set; }
        public decimal MonthlyGrowthRate { get; set; }
        public decimal YearlyGrowthRate { get; set; }
    }

    public class MonthlyTrend
    {
        public string Month { get; set; } = string.Empty;
        public int Year { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int InvoiceCount { get; set; }
        public int PaymentCount { get; set; }
        public double PaymentRate { get; set; }
        public decimal GrowthRate { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
    }

    public class SupplierStatistics
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public int TotalInvoices { get; set; }
        public int PaidInvoices { get; set; }
        public int UnpaidInvoices { get; set; }
        public int PartiallyPaidInvoices { get; set; }
        public int InvoiceCount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public double PaymentRate { get; set; }
        public TimeSpan AveragePaymentTime { get; set; }
        public DateTime LastInvoiceDate { get; set; }
        public DateTime LastPaymentDate { get; set; }
        public bool IsActive { get; set; }
        public string RiskLevel { get; set; } = "منخفض";
    }

    public class PaymentTrend
    {
        public string Period { get; set; } = string.Empty;
        public decimal CashPayments { get; set; }
        public decimal CardPayments { get; set; }
        public decimal TotalPayments { get; set; }
        public int PaymentCount { get; set; }
        public decimal AveragePaymentAmount { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
    }

    public class CashFlowAnalysis
    {
        public decimal CurrentMonthInflow { get; set; }
        public decimal CurrentMonthOutflow { get; set; }
        public decimal NetCashFlow { get; set; }
        public decimal ProjectedNextMonthInflow { get; set; }
        public decimal ProjectedNextMonthOutflow { get; set; }
        public decimal CashFlowTrend { get; set; }
        public IEnumerable<CashFlowItem> DailyFlows { get; set; } = new List<CashFlowItem>();
        public IEnumerable<CashFlowItem> WeeklyFlows { get; set; } = new List<CashFlowItem>();
        public IEnumerable<CashFlowItem> MonthlyFlows { get; set; } = new List<CashFlowItem>();
    }

    public class CashFlowItem
    {
        public DateTime Date { get; set; }
        public decimal Inflow { get; set; }
        public decimal Outflow { get; set; }
        public decimal NetFlow { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class AlertItem
    {
        public int Id { get; set; }
        public AlertType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public bool IsRead { get; set; } = false;
        public string ActionUrl { get; set; } = string.Empty;
        public AlertPriority Priority { get; set; } = AlertPriority.Normal;
        public DateTime? ExpiryDate { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public enum AlertType
    {
        Success,
        Warning,
        Error,
        Info,
        Critical
    }

    public enum AlertPriority
    {
        Low,
        Normal,
        High,
        Critical
    }

    public class ExportResult
    {
        public bool Success { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime ExportDate { get; set; }
        public ExportFormat Format { get; set; }
    }

    public enum ExportFormat
    {
        Excel,
        PDF,
        CSV,
        JSON
    }

    public class ExportOptions
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IncludeCharts { get; set; } = true;
        public bool IncludeStatistics { get; set; } = true;
        public bool IncludeRecentInvoices { get; set; } = true;
        public bool IncludeAlerts { get; set; } = false;
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public List<string> SelectedSuppliers { get; set; } = new();
        public List<InvoiceStatus> SelectedStatuses { get; set; } = new();
    }

    public class DashboardSettings
    {
        public int RefreshIntervalMinutes { get; set; } = 5;
        public bool AutoRefreshEnabled { get; set; } = true;
        public bool ShowAnimations { get; set; } = true;
        public bool ShowNotifications { get; set; } = true;
        public string DefaultChartType { get; set; } = "Line";
        public int DefaultChartPeriod { get; set; } = 6;
        public bool ShowQuickStats { get; set; } = true;
        public bool ShowRecentInvoices { get; set; } = true;
        public bool ShowAlerts { get; set; } = true;
        public int MaxRecentInvoices { get; set; } = 5;
        public int MaxAlerts { get; set; } = 10;
        public List<string> HiddenWidgets { get; set; } = new();
        public Dictionary<string, object> CustomSettings { get; set; } = new();
    }
}
