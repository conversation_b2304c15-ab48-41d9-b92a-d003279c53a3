<UserControl x:Class="HR_InvoiceArchiver.Controls.InvoiceFormControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
             FlowDirection="RightToLeft"
             Background="Transparent"
             MinWidth="550" MaxWidth="800" MinHeight="600" MaxHeight="900"
             HorizontalAlignment="Stretch"
             VerticalAlignment="Stretch">

    <UserControl.Resources>
        <converters:StatusToTextConverter x:Key="StatusToTextConverter"/>
        <converters:CurrencyConverter x:Key="CurrencyConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Modern Green-Teal Gradient Theme for Invoice -->
        <LinearGradientBrush x:Key="InvoiceGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#4CAF50" Offset="0"/>
            <GradientStop Color="#00BCD4" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#388E3C" Offset="0"/>
            <GradientStop Color="#00796B" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="AccentGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#66BB6A" Offset="0"/>
            <GradientStop Color="#26A69A" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Background" Value="{StaticResource InvoiceGradient}"/>
            <Setter Property="BorderBrush" Value="{StaticResource InvoiceGradient}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="24,12"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#4CAF50" BlurRadius="8" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#4CAF50" BlurRadius="12" ShadowDepth="3" Opacity="0.4"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="EnhancedTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="materialDesign:HintAssist.Foreground" Value="#4CAF50"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource InvoiceGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextBoxViewMargin" Value="0,0,0,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
            <Setter Property="materialDesign:TextFieldAssist.DecorationVisibility" Value="Collapsed"/>
            <Setter Property="materialDesign:TextFieldAssist.HasOutlinedTextField" Value="True"/>
        </Style>

        <!-- Modern ComboBox Style -->
        <Style x:Key="EnhancedComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="materialDesign:HintAssist.Foreground" Value="#4CAF50"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource InvoiceGradient}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="materialDesign:TextFieldAssist.HasOutlinedTextField" Value="True"/>
        </Style>

        <!-- Enhanced Slide In Animation -->
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                           From="0.8" To="1" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                           From="0.8" To="1" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)"
                           From="-50" To="0" Duration="0:0:0.5">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.4"/>
        </Storyboard>

        <!-- Enhanced Slide Out Animation -->
        <Storyboard x:Key="SlideOutAnimation" Completed="SlideOutAnimation_Completed">
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                           From="1" To="0.8" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                           From="1" To="0.8" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)"
                           From="0" To="50" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.3"/>
        </Storyboard>
        
        <!-- Save Success Animation -->
        <Storyboard x:Key="SaveSuccessAnimation">
            <DoubleAnimation Storyboard.TargetName="SaveButton"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1" To="1.1" Duration="0:0:0.15" AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="SaveButton"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1" To="1.1" Duration="0:0:0.15" AutoReverse="True"/>
        </Storyboard>

        <!-- Modern Card Style -->
        <Style x:Key="ModernInvoiceCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp16"/>

            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#4CAF50" BlurRadius="20" ShadowDepth="8" Opacity="0.15"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Text Shadow Effect -->
        <DropShadowEffect x:Key="TextShadowEffect" Color="#000000" BlurRadius="3" ShadowDepth="1" Opacity="0.3"/>



        <!-- Enhanced DatePicker Style -->
        <Style x:Key="EnhancedDatePickerStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="materialDesign:HintAssist.Foreground" Value="#2196F3"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource InvoiceGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>

        <!-- Section Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource HeaderGradient}"/>
            <Setter Property="CornerRadius" Value="8,8,0,0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#2196F3" BlurRadius="8" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Section Content Style -->
        <Style x:Key="SectionContentStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="0,0,8,8"/>
            <Setter Property="Padding" Value="25,20"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1,0,1,1"/>
        </Style>

        <!-- Status Indicator Panel Style -->
        <Style x:Key="StatusIndicatorStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- Success Toast Animation -->
        <Storyboard x:Key="SuccessToastAnimation">
            <DoubleAnimation Storyboard.TargetName="SuccessToast"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                           From="-100" To="0" Duration="0:0:0.5">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="SuccessToast"
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.3"/>
        </Storyboard>

        <!-- Hide Toast Animation -->
        <Storyboard x:Key="HideToastAnimation" Completed="HideToastAnimation_Completed">
            <DoubleAnimation Storyboard.TargetName="SuccessToast"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                           From="0" To="-100" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="SuccessToast"
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.2"/>
        </Storyboard>


    </UserControl.Resources>

    <!-- Main Container -->
    <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch">

        <!-- Main Invoice Card -->
        <materialDesign:Card x:Name="MainCard" Style="{StaticResource ModernInvoiceCardStyle}"
                            HorizontalAlignment="Center" VerticalAlignment="Center"
                            MinWidth="550" MaxWidth="800" MinHeight="600" MaxHeight="900"
                            Margin="20" materialDesign:ElevationAssist.Elevation="Dp4">

            <materialDesign:Card.RenderTransform>
                <TransformGroup>
                    <ScaleTransform/>
                    <TranslateTransform/>
                </TransformGroup>
            </materialDesign:Card.RenderTransform>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header Section -->
                <Border Grid.Row="0" Background="{StaticResource HeaderGradient}"
                        CornerRadius="20,20,0,0" Padding="30,25">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Icon with Glow Effect -->
                        <Border Grid.Column="0" Background="White" Width="60" Height="60" CornerRadius="30"
                                VerticalAlignment="Center" Margin="0,0,20,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#4CAF50" BlurRadius="15" ShadowDepth="0" Opacity="0.4"/>
                            </Border.Effect>
                            <materialDesign:PackIcon Kind="FileDocumentEdit" Width="32" Height="32"
                                                   Foreground="{StaticResource InvoiceGradient}"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <!-- Title with Enhanced Typography -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock x:Name="FormTitleTextBlock" Text="إضافة فاتورة جديدة"
                                      FontSize="26" FontWeight="Bold" Foreground="White"
                                      Effect="{StaticResource TextShadowEffect}"/>
                            <TextBlock Text="املأ البيانات المطلوبة لإنشاء الفاتورة"
                                      FontSize="14" Foreground="White" Opacity="0.9" Margin="0,5,0,0"/>
                        </StackPanel>

                        <!-- Modern Close Button -->
                        <Button Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}"
                               Click="CloseButton_Click" ToolTip="إغلاق" Width="45" Height="45"
                               Background="Transparent" Foreground="White"
                               materialDesign:RippleAssist.Feedback="White">
                            <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                        </Button>
                    </Grid>
                </Border>

                <!-- Content Section -->
                <Border Grid.Row="1" Background="#FAFAFA" Padding="35,30">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled"
                             Padding="0" Margin="0">
                    <StackPanel Margin="0">

                        <!-- Basic Information Section -->
                        <materialDesign:Card Padding="25" Margin="0,0,0,20"
                                           materialDesign:ElevationAssist.Elevation="Dp2">
                            <StackPanel>
                                <!-- Section Header -->
                                <Grid Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="{StaticResource AccentGradient}"
                                            Width="4" Height="24" CornerRadius="2" Margin="0,0,15,0"/>
                                    <TextBlock Grid.Column="1" Text="المعلومات الأساسية"
                                              FontSize="18" FontWeight="SemiBold"
                                              Foreground="{StaticResource HeaderGradient}"
                                              VerticalAlignment="Center"/>
                                </Grid>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="20"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="20"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Invoice Number -->
                                    <TextBox x:Name="InvoiceNumberTextBox" Grid.Row="0" Grid.Column="0"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="رقم الفاتورة *"
                                            Text="{Binding InvoiceNumber, UpdateSourceTrigger=PropertyChanged}"/>

                                    <!-- Invoice Date -->
                                    <DatePicker x:Name="InvoiceDatePicker" Grid.Row="0" Grid.Column="2"
                                               Style="{StaticResource EnhancedDatePickerStyle}"
                                               materialDesign:HintAssist.Hint="📅 تاريخ الفاتورة *"
                                               SelectedDate="{Binding InvoiceDate, UpdateSourceTrigger=PropertyChanged}"/>

                                    <!-- Supplier -->
                                    <ComboBox x:Name="SupplierComboBox" Grid.Row="2" Grid.ColumnSpan="3"
                                             Style="{StaticResource EnhancedComboBoxStyle}"
                                             materialDesign:HintAssist.Hint="المورد *"
                                             ItemsSource="{Binding Suppliers}"
                                             SelectedItem="{Binding SelectedSupplier, UpdateSourceTrigger=PropertyChanged}"
                                             DisplayMemberPath="Name"
                                             SelectedValuePath="Id"/>

                                    <!-- Description -->
                                    <TextBox x:Name="DescriptionTextBox" Grid.Row="4" Grid.ColumnSpan="3"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="وصف الفاتورة"
                                            Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                            AcceptsReturn="True"
                                            TextWrapping="Wrap"
                                            MinLines="3"
                                            MaxLines="5"/>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Financial Information Section -->
                        <materialDesign:Card Padding="25" Margin="0,0,0,20"
                                           materialDesign:ElevationAssist.Elevation="Dp2">
                            <StackPanel>
                                <!-- Section Header -->
                                <Grid Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="{StaticResource AccentGradient}"
                                            Width="4" Height="24" CornerRadius="2" Margin="0,0,15,0"/>
                                    <TextBlock Grid.Column="1" Text="المعلومات المالية"
                                              FontSize="18" FontWeight="SemiBold"
                                              Foreground="{StaticResource HeaderGradient}"
                                              VerticalAlignment="Center"/>
                                </Grid>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="20"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Total Amount -->
                                    <TextBox x:Name="TotalAmountTextBox" Grid.Row="0" Grid.Column="0"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="💰 المبلغ الإجمالي (د.ع) *"
                                            Text="{Binding TotalAmount, UpdateSourceTrigger=PropertyChanged}"/>

                                    <!-- Due Date -->
                                    <DatePicker x:Name="DueDatePicker" Grid.Row="0" Grid.Column="2"
                                               Style="{StaticResource EnhancedDatePickerStyle}"
                                               materialDesign:HintAssist.Hint="📅 تاريخ الاستحقاق"
                                               SelectedDate="{Binding DueDate, UpdateSourceTrigger=PropertyChanged}"/>

                                    <!-- حالة الفاتورة الأساسية -->
                                    <ComboBox x:Name="StatusComboBox" Grid.Row="2" Grid.ColumnSpan="3"
                                             Style="{StaticResource EnhancedComboBoxStyle}"
                                             materialDesign:HintAssist.Hint="📊 حالة الفاتورة الأساسية *"
                                             SelectionChanged="StatusComboBox_SelectionChanged">
                                        <ComboBoxItem Content="🔴 غير مسددة" Tag="Unpaid" Foreground="#FF5722"/>
                                        <ComboBoxItem Content="🟡 مسددة جزئياً" Tag="PartiallyPaid" Foreground="#FF9800"/>
                                        <ComboBoxItem Content="🟢 مسددة" Tag="Paid" Foreground="#4CAF50"/>
                                        <ComboBoxItem Content="🟣 مسددة وبخصم" Tag="PaidWithDiscount" Foreground="#9C27B0"/>
                                    </ComboBox>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- معلومات الدفعة المرفقة مع الفاتورة -->
                        <materialDesign:Card x:Name="PaymentInfoCard" Padding="25" Margin="0,0,0,20"
                                           materialDesign:ElevationAssist.Elevation="Dp2" Visibility="Collapsed">
                            <StackPanel>
                                <!-- Section Header -->
                                <Grid Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="{StaticResource AccentGradient}"
                                            Width="4" Height="24" CornerRadius="2" Margin="0,0,15,0"/>
                                    <TextBlock Grid.Column="1" Text="معلومات الدفعة المرفقة (اختياري)"
                                              FontSize="18" FontWeight="SemiBold"
                                              Foreground="{StaticResource HeaderGradient}"
                                              VerticalAlignment="Center"/>
                                </Grid>

                                <!-- Info Note -->
                                <Border Background="#E8F5E8" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                                    <TextBlock Text="💡 يمكنك إضافة دفعة مع الفاتورة مباشرة أو تركها فارغة وإضافة الدفعات لاحقاً"
                                              FontSize="13" Foreground="#2E7D32" TextWrapping="Wrap"/>
                                </Border>

                                <!-- نوع الدفعة المرفقة -->
                                <ComboBox x:Name="PaymentStatusComboBox"
                                         Style="{StaticResource EnhancedComboBoxStyle}"
                                         materialDesign:HintAssist.Hint="💳 نوع الدفعة المرفقة (اختياري)"
                                         SelectionChanged="PaymentStatusComboBox_SelectionChanged"
                                         Margin="0,0,0,20">
                                    <ComboBoxItem Content="🟡 دفعة جزئية" Tag="PartialPayment" Foreground="#FF9800"/>
                                    <ComboBoxItem Content="🟢 دفعة كاملة" Tag="FullPayment" Foreground="#4CAF50"/>
                                    <ComboBoxItem Content="🟣 دفعة مع خصم" Tag="PaymentWithDiscount" Foreground="#9C27B0"/>
                                </ComboBox>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="20"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="20"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Receipt Number -->
                                    <TextBox x:Name="ReceiptNumberTextBox" Grid.Row="0" Grid.Column="0"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="🧾 رقم الوصل *"
                                            Text="{Binding ReceiptNumber, UpdateSourceTrigger=PropertyChanged}"/>

                                    <!-- Payment Date -->
                                    <DatePicker x:Name="PaymentDatePicker" Grid.Row="0" Grid.Column="2"
                                               Style="{StaticResource EnhancedDatePickerStyle}"
                                               materialDesign:HintAssist.Hint="📅 تاريخ الدفع *"
                                               SelectedDate="{Binding PaymentDate, UpdateSourceTrigger=PropertyChanged}"/>

                                    <!-- Payment Amount -->
                                    <TextBox x:Name="PaymentAmountTextBox" Grid.Row="2" Grid.Column="0"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="💰 مبلغ الدفع (د.ع) *"
                                            Text="{Binding PaymentAmount, UpdateSourceTrigger=PropertyChanged}"
                                            PreviewTextInput="PaymentAmountTextBox_PreviewTextInput"
                                            TextChanged="PaymentAmountTextBox_TextChanged"/>

                                    <!-- Discount Amount (Conditional) -->
                                    <TextBox x:Name="DiscountAmountTextBox" Grid.Row="2" Grid.Column="2"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="🏷️ مبلغ الخصم (د.ع)"
                                            PreviewTextInput="DiscountAmountTextBox_PreviewTextInput"
                                            TextChanged="DiscountAmountTextBox_TextChanged"
                                            Visibility="Collapsed"/>

                                    <!-- Payment Method -->
                                    <ComboBox x:Name="PaymentMethodComboBox" Grid.Row="4" Grid.ColumnSpan="3"
                                             Style="{StaticResource EnhancedComboBoxStyle}"
                                             materialDesign:HintAssist.Hint="💳 طريقة الدفع *"
                                             SelectionChanged="PaymentMethodComboBox_SelectionChanged">
                                        <ComboBoxItem Content="💵 نقدي" Tag="Cash" Foreground="#4CAF50"/>
                                        <ComboBoxItem Content="💳 بطاقة بنكية" Tag="CreditCard" Foreground="#2196F3"/>
                                    </ComboBox>
                                </Grid>

                                <!-- Payment Notes -->
                                <TextBox x:Name="PaymentNotesTextBox"
                                        Style="{StaticResource EnhancedTextBoxStyle}"
                                        materialDesign:HintAssist.Hint="📝 ملاحظات الدفع"
                                        Text="{Binding PaymentNotes, UpdateSourceTrigger=PropertyChanged}"
                                        AcceptsReturn="True"
                                        TextWrapping="Wrap"
                                        MinLines="2"
                                        MaxLines="4"
                                        Margin="0,20,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Attachments Section -->
                        <materialDesign:Card Padding="25" Margin="0,0,0,20"
                                           materialDesign:ElevationAssist.Elevation="Dp2">
                            <StackPanel>
                                <!-- Section Header -->
                                <Grid Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="{StaticResource AccentGradient}"
                                            Width="4" Height="24" CornerRadius="2" Margin="0,0,15,0"/>
                                    <TextBlock Grid.Column="1" Text="المرفقات"
                                              FontSize="18" FontWeight="SemiBold"
                                              Foreground="{StaticResource HeaderGradient}"
                                              VerticalAlignment="Center"/>
                                </Grid>

                                <!-- Invoice Attachment -->
                                <Grid Margin="0,0,0,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="15"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox x:Name="AttachmentPathTextBox" Grid.Column="0"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="📎 مرفق الفاتورة"
                                            Text="{Binding AttachmentPath, UpdateSourceTrigger=PropertyChanged}"
                                            IsReadOnly="True"/>

                                    <Button Grid.Column="2" Style="{StaticResource MaterialDesignOutlinedButton}"
                                           Click="BrowseAttachmentButton_Click"
                                           Padding="15,12" Height="56"
                                           Foreground="{StaticResource InvoiceGradient}"
                                           BorderBrush="{StaticResource InvoiceGradient}"
                                           ToolTip="تصفح الملفات">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FolderOpen" Width="18" Height="18" Margin="0,0,8,0"/>
                                            <TextBlock Text="تصفح" FontWeight="SemiBold"/>
                                        </StackPanel>
                                    </Button>
                                </Grid>

                                <!-- Payment Receipt Attachment (Smart Logic) -->
                                <Grid x:Name="PaymentAttachmentGrid" Visibility="Collapsed">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="15"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox x:Name="PaymentAttachmentPathTextBox" Grid.Column="0"
                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="🧾 مرفق الوصل"
                                            Text="{Binding PaymentAttachmentPath, UpdateSourceTrigger=PropertyChanged}"
                                            IsReadOnly="True"/>

                                    <Button Grid.Column="2" Style="{StaticResource MaterialDesignOutlinedButton}"
                                           Click="BrowsePaymentAttachmentButton_Click"
                                           Padding="15,12" Height="56"
                                           Foreground="{StaticResource InvoiceGradient}"
                                           BorderBrush="{StaticResource InvoiceGradient}"
                                           ToolTip="تصفح الملفات">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18" Margin="0,0,8,0"/>
                                            <TextBlock Text="تصفح" FontWeight="SemiBold"/>
                                        </StackPanel>
                                    </Button>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                    </StackPanel>
                </ScrollViewer>
                </Border>

                <!-- Beautiful Action Buttons -->
                <Border Grid.Row="2" Background="{StaticResource HeaderGradient}"
                       CornerRadius="0,0,20,20" Padding="35,30" MinHeight="90">
                    <Border.Effect>
                        <DropShadowEffect Color="#4CAF50" BlurRadius="15" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <!-- Save Button -->
                        <Button x:Name="SaveButton" Click="SaveButton_Click"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Background="White" Foreground="{StaticResource InvoiceGradient}"
                               MinWidth="160" Height="60" Margin="0,0,25,0"
                               FontSize="16" FontWeight="Bold" Padding="30,18"
                               materialDesign:ButtonAssist.CornerRadius="30"
                               materialDesign:ElevationAssist.Elevation="Dp6"
                               BorderThickness="2" BorderBrush="White">
                            <Button.Effect>
                                <DropShadowEffect Color="White" BlurRadius="8" ShadowDepth="0" Opacity="0.4"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20" Margin="0,0,12,0"/>
                                <TextBlock x:Name="SaveButtonText" Text="حفظ الفاتورة" FontWeight="Bold"/>
                            </StackPanel>
                        </Button>

                        <!-- Cancel Button -->
                        <Button x:Name="CancelButton" Click="CancelButton_Click"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               BorderBrush="White" Foreground="White"
                               FontWeight="Bold" MinWidth="160" Height="60"
                               FontSize="16" Padding="30,18" BorderThickness="2"
                               materialDesign:ButtonAssist.CornerRadius="30">
                            <Button.Effect>
                                <DropShadowEffect Color="White" BlurRadius="8" ShadowDepth="0" Opacity="0.2"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cancel" Width="20" Height="20" Margin="0,0,12,0"/>
                                <TextBlock Text="إلغاء" FontWeight="Bold"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Border>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
