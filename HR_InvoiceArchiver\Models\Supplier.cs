using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace HR_InvoiceArchiver.Models
{
    public class Supplier
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ContactPerson { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(500)]
        public string? Address { get; set; }

        [StringLength(50)]
        public string? TaxNumber { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();

        // Calculated properties for UI display
        [NotMapped]
        public int InvoiceCount { get; set; }

        [NotMapped]
        public decimal TotalAmount { get; set; }

        // Calculated Properties
        [NotMapped]
        public decimal TotalInvoicesAmount => Invoices?.Sum(i => i.Amount) ?? 0;

        [NotMapped]
        public decimal TotalPaidAmount => Invoices?.Sum(i => i.PaidAmount) ?? 0;

        [NotMapped]
        public decimal RemainingAmount => TotalInvoicesAmount - TotalPaidAmount;

        [NotMapped]
        public int TotalInvoicesCount => Invoices?.Count ?? 0;

        [NotMapped]
        public int PaidInvoicesCount => Invoices?.Count(i => i.Status == InvoiceStatus.Paid) ?? 0;

        [NotMapped]
        public int UnpaidInvoicesCount => Invoices?.Count(i => i.Status == InvoiceStatus.Unpaid) ?? 0;

        [NotMapped]
        public int PartiallyPaidInvoicesCount => Invoices?.Count(i => i.Status == InvoiceStatus.PartiallyPaid) ?? 0;
    }
}
