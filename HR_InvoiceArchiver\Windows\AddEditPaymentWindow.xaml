<Window x:Class="HR_InvoiceArchiver.Windows.AddEditPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة/تعديل مدفوعة" 
        Height="700" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <Style x:Key="PaymentCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <materialDesign:PackIcon Grid.Column="0" Kind="CreditCard" Width="24" Height="24" Margin="0,0,12,0"/>
                <TextBlock Grid.Column="1" Text="{Binding WindowTitle}" FontSize="16" FontWeight="Medium" VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <materialDesign:Card Style="{StaticResource PaymentCardStyle}">
                <StackPanel>
                    <!-- Invoice Selection -->
                    <TextBlock Text="الفاتورة *" FontWeight="Medium" Margin="0,0,0,8"/>
                    <ComboBox x:Name="InvoiceComboBox" 
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            materialDesign:HintAssist.Hint="اختر الفاتورة"
                            DisplayMemberPath="DisplayText"
                            SelectedValuePath="Id"
                            SelectionChanged="InvoiceComboBox_SelectionChanged"
                            Margin="0,0,0,16"/>
                    
                    <!-- Invoice Info Panel -->
                    <Border x:Name="InvoiceInfoPanel" 
                          Background="{DynamicResource MaterialDesignCardBackground}" 
                          CornerRadius="8" Padding="16" Margin="0,0,0,16"
                          Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock Text="معلومات الفاتورة" FontWeight="Medium" Margin="0,0,0,8"/>
                            <TextBlock x:Name="InvoiceInfoTextBlock" TextWrapping="Wrap" 
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Receipt Number -->
                    <TextBlock Text="رقم الوصل *" FontWeight="Medium" Margin="0,0,0,8"/>
                    <TextBox x:Name="ReceiptNumberTextBox"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="أدخل رقم الوصل"
                           Margin="0,0,0,16"/>
                    
                    <!-- Payment Date -->
                    <TextBlock Text="تاريخ الدفع *" FontWeight="Medium" Margin="0,0,0,8"/>
                    <DatePicker x:Name="PaymentDatePicker" 
                              Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                              materialDesign:HintAssist.Hint="اختر تاريخ الدفع"
                              Margin="0,0,0,16"/>
                    
                    <!-- Amount -->
                    <TextBlock Text="المبلغ (دينار عراقي) *" FontWeight="Medium" Margin="0,0,0,8"/>
                    <TextBox x:Name="AmountTextBox" 
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="أدخل المبلغ"
                           Margin="0,0,0,16"/>
                    
                    <!-- Payment Status -->
                    <TextBlock Text="حالة التسديد *" FontWeight="Medium" Margin="0,0,0,8"/>
                    <ComboBox x:Name="PaymentStatusComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            materialDesign:HintAssist.Hint="اختر حالة التسديد"
                            SelectionChanged="PaymentStatusComboBox_SelectionChanged"
                            Margin="0,0,0,16">
                        <ComboBoxItem Content="تسديد كامل" Tag="FullPayment"/>
                        <ComboBoxItem Content="تسديد جزئي" Tag="PartialPayment"/>
                        <ComboBoxItem Content="تسديد مع خصم" Tag="PaymentWithDiscount"/>
                        <ComboBoxItem Content="تسديد واسترجاع المتبقي" Tag="PaymentWithRefund"/>
                    </ComboBox>

                    <!-- Discount Amount (visible only when PaymentWithDiscount is selected) -->
                    <TextBlock x:Name="DiscountLabel" Text="مبلغ الخصم (دينار عراقي)" FontWeight="Medium" Margin="0,0,0,8" Visibility="Collapsed"/>
                    <TextBox x:Name="DiscountAmountTextBox"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="أدخل مبلغ الخصم"
                           Visibility="Collapsed"
                           TextChanged="DiscountAmountTextBox_TextChanged"
                           Margin="0,0,0,16"/>

                    <!-- Refund Value (visible only when PaymentWithRefund is selected) -->
                    <TextBlock x:Name="RefundValueLabel" Text="قيمة الاسترجاع (دينار عراقي)" FontWeight="Medium" Margin="0,0,0,8" Visibility="Collapsed"/>
                    <TextBox x:Name="RefundValueTextBox"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="أدخل قيمة البضاعة المسترجعة"
                           Visibility="Collapsed"
                           TextChanged="RefundValueTextBox_TextChanged"
                           Margin="0,0,0,16"/>

                    <!-- Payment Method -->
                    <TextBlock Text="طريقة الدفع *" FontWeight="Medium" Margin="0,0,0,8"/>
                    <ComboBox x:Name="PaymentMethodComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            materialDesign:HintAssist.Hint="اختر طريقة الدفع"
                            Margin="0,0,0,16">
                        <ComboBoxItem Content="نقدي" Tag="Cash"/>
                        <ComboBoxItem Content="بطاقة" Tag="CreditCard"/>
                    </ComboBox>
                    
                    <!-- Notes -->
                    <TextBlock Text="الملاحظات" FontWeight="Medium" Margin="0,0,0,8"/>
                    <TextBox x:Name="NotesTextBox" 
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="أدخل ملاحظات إضافية (اختياري)"
                           Height="80" 
                           TextWrapping="Wrap" 
                           AcceptsReturn="True"
                           VerticalScrollBarVisibility="Auto"
                           Margin="0,0,0,16"/>
                    
                    <!-- Receipt Attachment -->
                    <TextBlock Text="مرفق الوصل" FontWeight="Medium" Margin="0,0,0,8"/>
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox x:Name="AttachmentPathTextBox" 
                               Grid.Column="0"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="مسار الملف المرفق"
                               IsReadOnly="True"
                               Margin="0,0,8,0"/>
                        
                        <Button Grid.Column="1" 
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Click="BrowseAttachmentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FolderOpen" Width="16" Height="16" Margin="0,0,4,0"/>
                                <TextBlock Text="تصفح"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}" Padding="16">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Click="SaveButton_Click"
                      MinWidth="100"
                      Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,4,0"/>
                        <TextBlock Text="حفظ"/>
                    </StackPanel>
                </Button>

                <Button x:Name="CancelButton"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Click="CancelButton_Click"
                      MinWidth="100"
                      Margin="8,0,0,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" Margin="0,0,4,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
