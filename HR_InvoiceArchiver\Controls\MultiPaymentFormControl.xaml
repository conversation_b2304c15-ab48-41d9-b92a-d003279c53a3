<UserControl x:Class="HR_InvoiceArchiver.Controls.MultiPaymentFormControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="Transparent"
             FlowDirection="RightToLeft"
             HorizontalAlignment="Stretch"
             VerticalAlignment="Stretch">

    <UserControl.RenderTransform>
        <ScaleTransform ScaleX="1" ScaleY="1"/>
    </UserControl.RenderTransform>
    <UserControl.RenderTransformOrigin>
        <Point X="0.5" Y="0.5"/>
    </UserControl.RenderTransformOrigin>

    <UserControl.Resources>
        <!-- Multi Payment Gradient -->
        <LinearGradientBrush x:Key="MultiPaymentGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF6B73" Offset="0"/>
            <GradientStop Color="#009FFF" Offset="1"/>
        </LinearGradientBrush>

        <!-- Success Gradient -->
        <LinearGradientBrush x:Key="SuccessGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#4CAF50" Offset="0"/>
            <GradientStop Color="#8BC34A" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource MultiPaymentGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource MultiPaymentGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>

        <!-- Modern DatePicker Style -->
        <Style x:Key="ModernDatePickerStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource MultiPaymentGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource MultiPaymentGradient}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="24,12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="25"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Invoice Item Style -->
        <Style x:Key="InvoiceItemStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{StaticResource MultiPaymentGradient}"/>
                    <Setter Property="Background" Value="#F8F9FF"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <!-- Main Container -->
    <Border Background="White" CornerRadius="20" Margin="20"
            MinWidth="1200" MaxWidth="1600" MinHeight="800" MaxHeight="1000"
            HorizontalAlignment="Center" VerticalAlignment="Center">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="20" ShadowDepth="5"/>
        </Border.Effect>

        <Border.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.4"/>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                       From="0.9" To="1.0" Duration="0:0:0.4">
                            <DoubleAnimation.EasingFunction>
                                <BackEase EasingMode="EaseOut" Amplitude="0.2"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                       From="0.9" To="1.0" Duration="0:0:0.4">
                            <DoubleAnimation.EasingFunction>
                                <BackEase EasingMode="EaseOut" Amplitude="0.2"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Border.Triggers>

        <Border.RenderTransform>
            <ScaleTransform ScaleX="1" ScaleY="1"/>
        </Border.RenderTransform>
        <Border.RenderTransformOrigin>
            <Point X="0.5" Y="0.5"/>
        </Border.RenderTransformOrigin>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" Background="{StaticResource MultiPaymentGradient}" CornerRadius="20,20,0,0" Padding="30,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CreditCardMultiple" Width="32" Height="32" 
                                               Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                        <StackPanel>
                            <TextBlock Text="إضافة وصل دفع متعدد" FontSize="24" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="دفع عدة فواتير بوصل واحد مع خصم ذكي" FontSize="14" Foreground="#E0E0E0" Margin="0,4,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <Button Grid.Column="1" x:Name="CloseButton" Click="CloseButton_Click"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="40" Height="40" Foreground="White">
                        <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30,20">
                <StackPanel>
                    <!-- Supplier Selection -->
                    <Border Background="#F8F9FA" CornerRadius="12" Padding="20" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="🏢 اختيار المورد" 
                                      FontSize="16" FontWeight="SemiBold" 
                                      Foreground="#FF6B73" Margin="0,0,0,12"/>
                            
                            <ComboBox x:Name="SupplierComboBox"
                                     Style="{StaticResource ModernComboBoxStyle}"
                                     materialDesign:HintAssist.Hint="اختر المورد أو ابحث..."
                                     DisplayMemberPath="Name"
                                     SelectedValuePath="Id"
                                     Foreground="#333333"
                                     Background="White"
                                     IsEditable="True"
                                     IsTextSearchEnabled="True"
                                     StaysOpenOnEdit="True"
                                     TextSearch.TextPath="Name"
                                     SelectionChanged="SupplierComboBox_SelectionChanged"/>
                        </StackPanel>
                    </Border>

                    <!-- Invoices Selection -->
                    <Border Background="#F8F9FA" CornerRadius="12" Padding="20" Margin="0,0,0,24">
                        <StackPanel>
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="📋 اختيار الفواتير"
                                          FontSize="16" FontWeight="SemiBold"
                                          Foreground="#009FFF"/>

                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button x:Name="SelectAllButton" Content="تحديد الكل"
                                           Style="{StaticResource MaterialDesignOutlinedButton}"
                                           FontSize="12" Padding="12,6" Margin="0,0,8,0"
                                           Click="SelectAllButton_Click"/>
                                    <Button x:Name="ClearAllButton" Content="إلغاء الكل"
                                           Style="{StaticResource MaterialDesignOutlinedButton}"
                                           FontSize="12" Padding="12,6"
                                           Click="ClearAllButton_Click"/>
                                </StackPanel>
                            </Grid>

                            <ScrollViewer x:Name="InvoicesScrollViewer" MaxHeight="400" VerticalScrollBarVisibility="Auto">
                                <ItemsControl x:Name="InvoicesItemsControl">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Style="{StaticResource InvoiceItemStyle}">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}"
                                                             VerticalAlignment="Center" Margin="0,0,12,0"
                                                             Checked="InvoiceCheckBox_Changed"
                                                             Unchecked="InvoiceCheckBox_Changed"/>

                                                    <StackPanel Grid.Column="1">
                                                        <TextBlock Text="{Binding InvoiceNumber}" FontWeight="SemiBold" FontSize="14"/>
                                                        <TextBlock Text="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}"
                                                                  FontSize="12" Foreground="#666666" Margin="0,2,0,0"/>
                                                    </StackPanel>

                                                    <StackPanel Grid.Column="2" HorizontalAlignment="Left">
                                                        <TextBlock Text="{Binding RemainingAmount, StringFormat={}{0:N0} د.ع}"
                                                                  FontWeight="SemiBold" FontSize="14" Foreground="#FF6B73"/>
                                                        <TextBlock Text="{Binding StatusText}"
                                                                  FontSize="11" Foreground="#999999"/>
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </StackPanel>
                    </Border>

                    <!-- Payment Summary -->
                    <Border x:Name="PaymentSummaryPanel" Background="#E8F5E8" CornerRadius="12" Padding="20" Margin="0,0,0,24" Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock Text="💰 ملخص الدفع"
                                      FontSize="16" FontWeight="SemiBold"
                                      Foreground="#4CAF50" Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Left Column -->
                                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,12">
                                        <StackPanel>
                                            <TextBlock Text="عدد الفواتير" FontSize="12" Foreground="#666666"/>
                                            <TextBlock x:Name="InvoicesCountText" Text="0" FontSize="20" FontWeight="Bold" Foreground="#4CAF50"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Background="White" CornerRadius="8" Padding="16">
                                        <StackPanel>
                                            <TextBlock Text="إجمالي المبلغ" FontSize="12" Foreground="#666666"/>
                                            <TextBlock x:Name="TotalAmountText" Text="0 د.ع" FontSize="18" FontWeight="Bold" Foreground="#FF6B73"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>

                                <!-- Right Column -->
                                <StackPanel Grid.Column="1">
                                    <!-- Discount Section -->
                                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,12">
                                        <StackPanel>
                                            <TextBlock Text="الخصم المكتسب" FontSize="12" Foreground="#666666" Margin="0,0,0,8"/>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBox x:Name="DiscountPercentageTextBox" Grid.Column="0"
                                                        Style="{StaticResource ModernTextBoxStyle}"
                                                        materialDesign:HintAssist.Hint="النسبة %"
                                                        FontSize="14" Margin="0,0,8,0"
                                                        TextChanged="DiscountPercentage_TextChanged"/>

                                                <Button Grid.Column="1" Content="ذكي"
                                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                                       FontSize="12" Padding="12,8"
                                                       Click="SmartDiscountButton_Click"/>
                                            </Grid>

                                            <TextBlock x:Name="DiscountAmountText" Text="0 د.ع"
                                                      FontSize="16" FontWeight="SemiBold" Foreground="#009FFF"
                                                      HorizontalAlignment="Center" Margin="0,8,0,0"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Final Amount -->
                                    <Border Background="{StaticResource SuccessGradient}" CornerRadius="8" Padding="16">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="المبلغ النهائي" FontSize="12" Foreground="White" HorizontalAlignment="Center"/>
                                            <TextBlock x:Name="FinalAmountText" Text="0 د.ع"
                                                      FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                    <!-- Payment Details -->
                    <Border x:Name="PaymentDetailsPanel" Background="#F8F9FA" CornerRadius="12" Padding="20" Margin="0,0,0,24" Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock Text="📝 تفاصيل الدفع"
                                      FontSize="16" FontWeight="SemiBold"
                                      Foreground="#FF6B73" Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Left Column -->
                                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                                    <TextBox x:Name="ReceiptNumberTextBox"
                                            Style="{StaticResource ModernTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="رقم الوصل"/>

                                    <DatePicker x:Name="PaymentDatePicker"
                                               Style="{StaticResource ModernDatePickerStyle}"
                                               materialDesign:HintAssist.Hint="تاريخ الدفع"/>

                                    <ComboBox x:Name="PaymentMethodComboBox"
                                             Style="{StaticResource ModernComboBoxStyle}"
                                             materialDesign:HintAssist.Hint="طريقة الدفع">
                                        <ComboBoxItem Content="نقدي"/>
                                        <ComboBoxItem Content="بطاقة بنكية"/>
                                    </ComboBox>
                                </StackPanel>

                                <!-- Right Column -->
                                <StackPanel Grid.Column="1">
                                    <TextBox x:Name="NotesTextBox"
                                            Style="{StaticResource ModernTextBoxStyle}"
                                            materialDesign:HintAssist.Hint="ملاحظات"
                                            AcceptsReturn="True"
                                            TextWrapping="Wrap"
                                            Height="120"/>

                                    <!-- Attachment Section -->
                                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,20">
                                        <StackPanel>
                                            <TextBlock Text="📎 مرفق الوصل" FontSize="14" FontWeight="SemiBold" Margin="0,0,0,8"/>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBox x:Name="AttachmentPathTextBox" Grid.Column="0"
                                                        IsReadOnly="True"
                                                        materialDesign:HintAssist.Hint="لم يتم اختيار ملف"
                                                        FontSize="12" Margin="0,0,8,0"/>

                                                <Button Grid.Column="1" Content="اختيار ملف"
                                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                                       FontSize="12" Padding="12,8"
                                                       Click="SelectAttachmentButton_Click"/>
                                            </Grid>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>

            <!-- Footer Buttons -->
            <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="0,0,20,20" Padding="30,25" MinHeight="80">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Status Text -->
                    <TextBlock x:Name="StatusTextBlock" Grid.Column="0"
                              Text="اختر المورد والفواتير للمتابعة"
                              FontSize="14" Foreground="#666666"
                              VerticalAlignment="Center"/>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button x:Name="CancelButton" Content="إلغاء"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               FontSize="14" Padding="24,16" Margin="0,0,12,0" MinHeight="45"
                               Click="CancelButton_Click"/>

                        <Button x:Name="SaveButton"
                               Style="{StaticResource ModernButtonStyle}"
                               FontSize="14" Padding="24,16" MinHeight="45"
                               IsEnabled="False"
                               Click="SaveButton_Click">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="حفظ الوصل المتعدد"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
