<UserControl x:Class="HR_InvoiceArchiver.Pages.SettingsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16" CornerRadius="4" Margin="0,0,0,24">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Settings" Width="24" Height="24" VerticalAlignment="Center" Margin="0,0,12,0"/>
                <TextBlock Text="الإعدادات" Style="{StaticResource MaterialDesignHeadline6TextBlock}" VerticalAlignment="Center"/>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- Content -->
        <materialDesign:Card Grid.Row="1" materialDesign:ElevationAssist.Elevation="Dp4">
            <Grid Margin="32">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Settings" Width="64" Height="64" 
                                           Foreground="{DynamicResource MaterialDesignBodyLight}" 
                                           HorizontalAlignment="Center" Margin="0,0,0,16"/>
                    <TextBlock Text="صفحة الإعدادات قيد التطوير" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center" 
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    <TextBlock Text="سيتم إضافة إعدادات التطبيق قريباً" 
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             HorizontalAlignment="Center" 
                             Margin="0,8,0,0"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
