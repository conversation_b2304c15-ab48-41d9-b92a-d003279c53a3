﻿#pragma checksum "..\..\..\..\Controls\SupplierStatementControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4D114178B6364D7FCCEA8184A235B872D1C341CA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Controls {
    
    
    /// <summary>
    /// SupplierStatementControl
    /// </summary>
    public partial class SupplierStatementControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 68 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid RootGrid;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle BackgroundOverlay;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card MainCard;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierNameText;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidAmountText;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountText;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceCountText;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 340 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 347 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 409 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 428 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 451 "..\..\..\..\Controls\SupplierStatementControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar LoadingProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/controls/supplierstatementcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\SupplierStatementControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 28 "..\..\..\..\Controls\SupplierStatementControl.xaml"
            ((System.Windows.Media.Animation.Storyboard)(target)).Completed += new System.EventHandler(this.SlideOutAnimation_Completed);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RootGrid = ((System.Windows.Controls.Grid)(target));
            
            #line 68 "..\..\..\..\Controls\SupplierStatementControl.xaml"
            this.RootGrid.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.RootGrid_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BackgroundOverlay = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 4:
            this.MainCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 5:
            this.SupplierNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 133 "..\..\..\..\Controls\SupplierStatementControl.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TotalInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.PaidAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.RemainingAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.InvoiceCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 13:
            this.PaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 15:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 398 "..\..\..\..\Controls\SupplierStatementControl.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 417 "..\..\..\..\Controls\SupplierStatementControl.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 435 "..\..\..\..\Controls\SupplierStatementControl.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.LoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

