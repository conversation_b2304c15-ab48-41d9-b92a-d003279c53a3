using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Controls;

namespace HR_InvoiceArchiver.Services
{
    public interface IToastService
    {
        void ShowSuccess(string title, string message, TimeSpan? duration = null);
        void ShowError(string title, string message, TimeSpan? duration = null);
        void ShowWarning(string title, string message, TimeSpan? duration = null);
        void ShowInfo(string title, string message, TimeSpan? duration = null);
        void Clear();
        void SetContainer(Panel container);
    }

    public class ToastService : IToastService
    {
        private Panel? _container;
        private readonly List<ToastNotification> _activeToasts = new();
        private const int MaxToasts = 5;

        public void SetContainer(Panel container)
        {
            _container = container;
        }

        public void ShowSuccess(string title, string message, TimeSpan? duration = null)
        {
            ShowToast(ToastNotification.ToastType.Success, title, message, duration);
        }

        public void ShowError(string title, string message, TimeSpan? duration = null)
        {
            ShowToast(ToastNotification.ToastType.Error, title, message, duration ?? TimeSpan.FromSeconds(6));
        }

        public void ShowWarning(string title, string message, TimeSpan? duration = null)
        {
            ShowToast(ToastNotification.ToastType.Warning, title, message, duration ?? TimeSpan.FromSeconds(5));
        }

        public void ShowInfo(string title, string message, TimeSpan? duration = null)
        {
            ShowToast(ToastNotification.ToastType.Info, title, message, duration);
        }

        private void ShowToast(ToastNotification.ToastType type, string title, string message, TimeSpan? duration = null)
        {
            if (_container == null)
            {
                // Fallback to MessageBox if no container is set - ensure it's on UI thread
                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(message, title, MessageBoxButton.OK, GetMessageBoxImage(type));
                });
                return;
            }

            Application.Current.Dispatcher.Invoke(() =>
            {
                // Remove oldest toast if we have too many
                if (_activeToasts.Count >= MaxToasts)
                {
                    var oldestToast = _activeToasts.First();
                    RemoveToast(oldestToast);
                }

                var toast = new ToastNotification
                {
                    Type = type,
                    Title = title,
                    Message = message,
                    Duration = duration ?? TimeSpan.FromSeconds(4),
                    VerticalAlignment = VerticalAlignment.Top,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(0, _activeToasts.Count * 80, 0, 0)
                };

                toast.Closed += (s, e) => RemoveToast(toast);

                _container.Children.Add(toast);
                _activeToasts.Add(toast);

                toast.Show();

                // Adjust positions of existing toasts
                UpdateToastPositions();
            });
        }

        private void RemoveToast(ToastNotification toast)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                if (_container?.Children.Contains(toast) == true)
                {
                    _container.Children.Remove(toast);
                }

                _activeToasts.Remove(toast);
                UpdateToastPositions();
            });
        }

        private void UpdateToastPositions()
        {
            for (int i = 0; i < _activeToasts.Count; i++)
            {
                _activeToasts[i].Margin = new Thickness(0, i * 80, 0, 0);
            }
        }

        public void Clear()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var toastsToRemove = _activeToasts.ToList();
                foreach (var toast in toastsToRemove)
                {
                    toast.Close();
                }
            });
        }

        private static MessageBoxImage GetMessageBoxImage(ToastNotification.ToastType type)
        {
            return type switch
            {
                ToastNotification.ToastType.Success => MessageBoxImage.Information,
                ToastNotification.ToastType.Error => MessageBoxImage.Error,
                ToastNotification.ToastType.Warning => MessageBoxImage.Warning,
                ToastNotification.ToastType.Info => MessageBoxImage.Information,
                _ => MessageBoxImage.Information
            };
        }
    }

    // Extension methods for easy usage
    public static class ToastExtensions
    {
        public static void ShowSuccessToast(this IToastService toastService, string message)
        {
            toastService.ShowSuccess("نجح العمل", message);
        }

        public static void ShowErrorToast(this IToastService toastService, string message)
        {
            toastService.ShowError("خطأ", message);
        }

        public static void ShowWarningToast(this IToastService toastService, string message)
        {
            toastService.ShowWarning("تحذير", message);
        }

        public static void ShowInfoToast(this IToastService toastService, string message)
        {
            toastService.ShowInfo("معلومات", message);
        }
    }
}
