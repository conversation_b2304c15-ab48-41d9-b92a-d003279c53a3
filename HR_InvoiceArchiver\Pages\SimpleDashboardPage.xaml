<UserControl x:Class="HR_InvoiceArchiver.Pages.SimpleDashboardPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <Grid Background="LightBlue">
        <StackPanel Margin="50" HorizontalAlignment="Center" VerticalAlignment="Center">
            <materialDesign:Card Padding="32" materialDesign:ElevationAssist.Elevation="Dp4">
                <StackPanel>
                    <materialDesign:PackIcon Kind="ViewDashboard" 
                                           Width="64" Height="64" 
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    
                    <TextBlock Text="مرحباً بك في لوحة التحكم البسيطة" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Margin="0,16,0,8"/>
                    
                    <TextBlock Text="تم تحميل لوحة التحكم بنجاح!" 
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    
                    <TextBlock Text="هذه صفحة اختبار بسيطة" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             HorizontalAlignment="Center"
                             Margin="0,8,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </Grid>
</UserControl>
