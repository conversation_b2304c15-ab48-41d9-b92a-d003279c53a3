using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Data.Repositories;
using Microsoft.Extensions.Logging;

namespace HR_InvoiceArchiver.Services
{
    public class DashboardService : IDashboardService
    {
        private readonly IInvoiceRepository _invoiceRepository;
        private readonly ISupplierRepository _supplierRepository;
        private readonly IPaymentRepository _paymentRepository;
        private readonly ILogger<DashboardService> _logger;
        private readonly List<AlertItem> _alerts = new();
        private DashboardSettings _settings = new();

        public DashboardService(
            IInvoiceRepository invoiceRepository,
            ISupplierRepository supplierRepository,
            IPaymentRepository paymentRepository,
            ILogger<DashboardService> logger)
        {
            _invoiceRepository = invoiceRepository;
            _supplierRepository = supplierRepository;
            _paymentRepository = paymentRepository;
            _logger = logger;
        }

        public async Task<DashboardStatistics> GetDashboardStatisticsAsync()
        {
            try
            {
                var invoices = await _invoiceRepository.GetAllAsync();
                var suppliers = await _supplierRepository.GetAllAsync();
                var payments = await _paymentRepository.GetAllAsync();

                var totalInvoices = invoices.Count();
                var totalAmount = invoices.Sum(i => i.Amount);
                var paidAmount = invoices.Sum(i => i.PaidAmount);
                var outstandingAmount = totalAmount - paidAmount;

                var unpaidCount = invoices.Count(i => i.Status == InvoiceStatus.Unpaid);
                var partiallyPaidCount = invoices.Count(i => i.Status == InvoiceStatus.PartiallyPaid);
                var paidCount = invoices.Count(i => i.Status == InvoiceStatus.Paid);

                var overdueInvoices = invoices.Where(i =>
                    i.Status != InvoiceStatus.Paid &&
                    i.InvoiceDate.AddDays(30) < DateTime.Now).Count();

                var paymentRate = totalInvoices > 0 ? (double)paidCount / totalInvoices * 100 : 0;

                // Calculate advanced statistics
                var averageInvoiceAmount = totalInvoices > 0 ? totalAmount / totalInvoices : 0;
                var largestInvoiceAmount = invoices.Any() ? invoices.Max(i => i.Amount) : 0;
                var smallestInvoiceAmount = invoices.Any() ? invoices.Min(i => i.Amount) : 0;

                var totalSuppliers = suppliers.Count();
                var activeSuppliers = suppliers.Count(s => invoices.Any(i => i.SupplierId == s.Id));

                // Calculate average payment time
                var paidInvoices = invoices.Where(i => i.Status == InvoiceStatus.Paid);
                var averagePaymentTime = paidInvoices.Any()
                    ? TimeSpan.FromDays(paidInvoices.Average(i => i.Payments.Any() ? i.Payments.Max(p => p.PaymentDate).Subtract(i.InvoiceDate).TotalDays : 0))
                    : TimeSpan.Zero;

                // Calculate growth rates
                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;
                var lastMonth = currentMonth == 1 ? 12 : currentMonth - 1;
                var lastMonthYear = currentMonth == 1 ? currentYear - 1 : currentYear;

                var currentMonthAmount = invoices
                    .Where(i => i.InvoiceDate.Month == currentMonth && i.InvoiceDate.Year == currentYear)
                    .Sum(i => i.Amount);

                var lastMonthAmount = invoices
                    .Where(i => i.InvoiceDate.Month == lastMonth && i.InvoiceDate.Year == lastMonthYear)
                    .Sum(i => i.Amount);

                var monthlyGrowthRate = lastMonthAmount > 0 
                    ? (currentMonthAmount - lastMonthAmount) / lastMonthAmount * 100 
                    : 0;

                var currentYearAmount = invoices
                    .Where(i => i.InvoiceDate.Year == currentYear)
                    .Sum(i => i.Amount);

                var lastYearAmount = invoices
                    .Where(i => i.InvoiceDate.Year == currentYear - 1)
                    .Sum(i => i.Amount);

                var yearlyGrowthRate = lastYearAmount > 0 
                    ? (currentYearAmount - lastYearAmount) / lastYearAmount * 100 
                    : 0;

                return new DashboardStatistics
                {
                    TotalInvoices = totalInvoices,
                    TotalAmount = totalAmount,
                    PaidAmount = paidAmount,
                    OutstandingAmount = outstandingAmount,
                    OverdueInvoices = overdueInvoices,
                    UnpaidCount = unpaidCount,
                    PartiallyPaidCount = partiallyPaidCount,
                    PaidCount = paidCount,
                    PaymentRate = paymentRate,
                    LastUpdated = DateTime.Now,
                    AverageInvoiceAmount = averageInvoiceAmount,
                    LargestInvoiceAmount = largestInvoiceAmount,
                    SmallestInvoiceAmount = smallestInvoiceAmount,
                    TotalSuppliers = totalSuppliers,
                    ActiveSuppliers = activeSuppliers,
                    AveragePaymentTime = averagePaymentTime,
                    MonthlyGrowthRate = (decimal)monthlyGrowthRate,
                    YearlyGrowthRate = (decimal)yearlyGrowthRate
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard statistics");
                throw;
            }
        }

        public async Task<IEnumerable<MonthlyTrend>> GetMonthlyTrendsAsync(int months = 6)
        {
            try
            {
                var invoices = await _invoiceRepository.GetAllAsync();
                var payments = await _paymentRepository.GetAllAsync();
                var trends = new List<MonthlyTrend>();

                var startDate = DateTime.Now.AddMonths(-months);

                for (int i = 0; i < months; i++)
                {
                    var periodStart = startDate.AddMonths(i);
                    var periodEnd = periodStart.AddMonths(1).AddDays(-1);

                    var monthInvoices = invoices.Where(inv => 
                        inv.InvoiceDate >= periodStart && inv.InvoiceDate <= periodEnd);

                    var monthPayments = payments.Where(p => 
                        p.PaymentDate >= periodStart && p.PaymentDate <= periodEnd);

                    var totalAmount = monthInvoices.Sum(i => i.Amount);
                    var paidAmount = monthInvoices.Sum(i => i.PaidAmount);
                    var outstandingAmount = totalAmount - paidAmount;
                    var invoiceCount = monthInvoices.Count();
                    var paymentCount = monthPayments.Count();
                    var paymentRate = invoiceCount > 0 ? (double)monthInvoices.Count(i => i.Status == InvoiceStatus.Paid) / invoiceCount * 100 : 0;

                    // Calculate growth rate compared to previous month
                    var previousMonthAmount = i > 0 ? trends[i - 1].TotalAmount : 0;
                    var growthRate = previousMonthAmount > 0 ? (totalAmount - previousMonthAmount) / previousMonthAmount * 100 : 0;

                    trends.Add(new MonthlyTrend
                    {
                        Month = periodStart.ToString("MMMM"),
                        Year = periodStart.Year,
                        TotalAmount = totalAmount,
                        PaidAmount = paidAmount,
                        OutstandingAmount = outstandingAmount,
                        InvoiceCount = invoiceCount,
                        PaymentCount = paymentCount,
                        PaymentRate = paymentRate,
                        GrowthRate = growthRate,
                        PeriodStart = periodStart,
                        PeriodEnd = periodEnd
                    });
                }

                return trends;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting monthly trends");
                throw;
            }
        }

        public async Task<IEnumerable<SupplierStatistics>> GetTopSuppliersAsync(int count = 10)
        {
            try
            {
                var invoices = await _invoiceRepository.GetAllAsync();
                var suppliers = await _supplierRepository.GetAllAsync();
                var payments = await _paymentRepository.GetAllAsync();

                var supplierStats = suppliers.Select(supplier =>
                {
                    var supplierInvoices = invoices.Where(i => i.SupplierId == supplier.Id);
                    var supplierPayments = payments.Where(p => supplierInvoices.Any(i => i.Id == p.InvoiceId));

                    var totalAmount = supplierInvoices.Sum(i => i.Amount);
                    var paidAmount = supplierInvoices.Sum(i => i.PaidAmount);
                    var outstandingAmount = totalAmount - paidAmount;
                    var invoiceCount = supplierInvoices.Count();
                    var paymentRate = invoiceCount > 0 ? (double)supplierInvoices.Count(i => i.Status == InvoiceStatus.Paid) / invoiceCount * 100 : 0;

                    var paidInvoices = supplierInvoices.Where(i => i.Status == InvoiceStatus.Paid);
                    var averagePaymentTime = paidInvoices.Any()
                        ? TimeSpan.FromDays(paidInvoices.Average(i => i.Payments.Any() ? i.Payments.Max(p => p.PaymentDate).Subtract(i.InvoiceDate).TotalDays : 0))
                        : TimeSpan.Zero;

                    var lastInvoiceDate = supplierInvoices.Any() ? supplierInvoices.Max(i => i.InvoiceDate) : DateTime.MinValue;
                    var lastPaymentDate = supplierPayments.Any() ? supplierPayments.Max(p => p.PaymentDate) : DateTime.MinValue;

                    var isActive = lastInvoiceDate > DateTime.Now.AddMonths(-3);

                    // Determine risk level based on payment behavior
                    var riskLevel = "منخفض";
                    if (paymentRate < 50) riskLevel = "عالي";
                    else if (paymentRate < 80) riskLevel = "متوسط";

                    return new SupplierStatistics
                    {
                        SupplierId = supplier.Id,
                        SupplierName = supplier.Name,
                        InvoiceCount = invoiceCount,
                        TotalAmount = totalAmount,
                        PaidAmount = paidAmount,
                        OutstandingAmount = outstandingAmount,
                        PaymentRate = paymentRate,
                        AveragePaymentTime = averagePaymentTime,
                        LastInvoiceDate = lastInvoiceDate,
                        LastPaymentDate = lastPaymentDate,
                        IsActive = isActive,
                        RiskLevel = riskLevel
                    };
                })
                .OrderByDescending(s => s.TotalAmount)
                .Take(count);

                return supplierStats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top suppliers");
                throw;
            }
        }

        public async Task<IEnumerable<PaymentTrend>> GetPaymentTrendsAsync(int months = 6)
        {
            try
            {
                var payments = await _paymentRepository.GetAllAsync();
                var trends = new List<PaymentTrend>();

                var startDate = DateTime.Now.AddMonths(-months);

                for (int i = 0; i < months; i++)
                {
                    var periodStart = startDate.AddMonths(i);
                    var periodEnd = periodStart.AddMonths(1).AddDays(-1);

                    var monthPayments = payments.Where(p =>
                        p.PaymentDate >= periodStart && p.PaymentDate <= periodEnd);

                    var cashPayments = monthPayments.Where(p => p.Method == PaymentMethod.Cash).Sum(p => p.Amount);
                    var cardPayments = monthPayments.Where(p => p.Method == PaymentMethod.CreditCard).Sum(p => p.Amount);
                    var totalPayments = monthPayments.Sum(p => p.Amount);
                    var paymentCount = monthPayments.Count();
                    var averagePaymentAmount = paymentCount > 0 ? totalPayments / paymentCount : 0;

                    trends.Add(new PaymentTrend
                    {
                        Period = periodStart.ToString("yyyy-MM"),
                        CashPayments = cashPayments,
                        CardPayments = cardPayments,
                        TotalPayments = totalPayments,
                        PaymentCount = paymentCount,
                        AveragePaymentAmount = averagePaymentAmount,
                        PeriodStart = periodStart,
                        PeriodEnd = periodEnd
                    });
                }

                return trends;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment trends");
                throw;
            }
        }

        public async Task<CashFlowAnalysis> GetCashFlowAnalysisAsync()
        {
            try
            {
                var invoices = await _invoiceRepository.GetAllAsync();
                var payments = await _paymentRepository.GetAllAsync();

                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;

                var currentMonthStart = new DateTime(currentYear, currentMonth, 1);
                var currentMonthEnd = currentMonthStart.AddMonths(1).AddDays(-1);

                var currentMonthInvoices = invoices.Where(i =>
                    i.InvoiceDate >= currentMonthStart && i.InvoiceDate <= currentMonthEnd);
                var currentMonthPayments = payments.Where(p =>
                    p.PaymentDate >= currentMonthStart && p.PaymentDate <= currentMonthEnd);

                var currentMonthInflow = currentMonthPayments.Sum(p => p.Amount);
                var currentMonthOutflow = currentMonthInvoices.Sum(i => i.Amount);
                var netCashFlow = currentMonthInflow - currentMonthOutflow;

                // Simple projection based on average of last 3 months
                var last3MonthsInflow = payments.Where(p => p.PaymentDate >= currentMonthStart.AddMonths(-3) && p.PaymentDate < currentMonthStart)
                    .GroupBy(p => new { p.PaymentDate.Year, p.PaymentDate.Month })
                    .Select(g => g.Sum(p => p.Amount))
                    .DefaultIfEmpty(0)
                    .Average();

                var projectedNextMonthInflow = (decimal)last3MonthsInflow;

                // Calculate trend
                var previousMonthInflow = payments.Where(p =>
                    p.PaymentDate >= currentMonthStart.AddMonths(-1) && p.PaymentDate < currentMonthStart)
                    .Sum(p => p.Amount);

                var cashFlowTrend = previousMonthInflow > 0
                    ? (currentMonthInflow - previousMonthInflow) / previousMonthInflow * 100
                    : 0;

                return new CashFlowAnalysis
                {
                    CurrentMonthInflow = currentMonthInflow,
                    CurrentMonthOutflow = currentMonthOutflow,
                    NetCashFlow = netCashFlow,
                    ProjectedNextMonthInflow = projectedNextMonthInflow,
                    ProjectedNextMonthOutflow = 0,
                    CashFlowTrend = cashFlowTrend,
                    DailyFlows = new List<CashFlowItem>()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cash flow analysis");
                throw;
            }
        }

        public async Task<IEnumerable<AlertItem>> GetDashboardAlertsAsync()
        {
            try
            {
                var alerts = new List<AlertItem>();
                var invoices = await _invoiceRepository.GetAllAsync();

                // Overdue invoices alert
                var overdueInvoices = invoices.Where(i =>
                    i.Status != InvoiceStatus.Paid &&
                    i.InvoiceDate.AddDays(30) < DateTime.Now);

                if (overdueInvoices.Any())
                {
                    alerts.Add(new AlertItem
                    {
                        Id = 1,
                        Type = AlertType.Warning,
                        Title = "فواتير متأخرة",
                        Message = $"لديك {overdueInvoices.Count()} فاتورة متأخرة السداد",
                        Icon = "AlertCircle",
                        Priority = AlertPriority.High,
                        Timestamp = DateTime.Now,
                        ActionUrl = "/invoices?filter=overdue"
                    });
                }

                return alerts.Concat(_alerts).OrderByDescending(a => a.Priority).ThenByDescending(a => a.Timestamp);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard alerts");
                throw;
            }
        }

        public async Task<ExportResult> ExportDashboardDataAsync(ExportFormat format, ExportOptions options)
        {
            try
            {
                // TODO: Implement export functionality
                await Task.Delay(1000); // Simulate export process

                return new ExportResult
                {
                    Success = true,
                    FilePath = $"dashboard_export_{DateTime.Now:yyyyMMdd_HHmmss}.{format.ToString().ToLower()}",
                    FileSize = 1024,
                    ExportDate = DateTime.Now,
                    Format = format
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting dashboard data");
                return new ExportResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public Task<bool> MarkAlertAsReadAsync(int alertId)
        {
            try
            {
                var alert = _alerts.FirstOrDefault(a => a.Id == alertId);
                if (alert != null)
                {
                    alert.IsRead = true;
                    return Task.FromResult(true);
                }
                return Task.FromResult(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking alert as read");
                return Task.FromResult(false);
            }
        }

        public Task<bool> ClearAllAlertsAsync()
        {
            try
            {
                _alerts.Clear();
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing alerts");
                return Task.FromResult(false);
            }
        }

        public Task<DashboardSettings> GetDashboardSettingsAsync()
        {
            try
            {
                return Task.FromResult(_settings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard settings");
                throw;
            }
        }

        public Task<bool> UpdateDashboardSettingsAsync(DashboardSettings settings)
        {
            try
            {
                _settings = settings;
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating dashboard settings");
                return Task.FromResult(false);
            }
        }
    }
}