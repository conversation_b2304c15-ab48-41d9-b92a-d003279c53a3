using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Models;
using System.IO;

namespace HR_InvoiceArchiver.Data
{
    public class DatabaseContext : DbContext
    {
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<Payment> Payments { get; set; }

        public DatabaseContext()
        {
        }

        public DatabaseContext(DbContextOptions<DatabaseContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                                        "HR_InvoiceArchiver", "InvoiceArchiver.db");

                // Create directory if it doesn't exist
                var directory = Path.GetDirectoryName(dbPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Supplier Configuration
            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ContactPerson).HasMaxLength(100);
                entity.Property(e => e.Phone).HasMaxLength(20);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Address).HasMaxLength(500);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.IsActive).IsRequired().HasDefaultValue(true);

                entity.HasIndex(e => e.Name).IsUnique();
            });

            // Invoice Configuration
            modelBuilder.Entity<Invoice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Amount).IsRequired().HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.InvoiceDate).IsRequired();
                entity.Property(e => e.Status).IsRequired().HasDefaultValue(InvoiceStatus.Unpaid);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.AttachmentPath).HasMaxLength(500);
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.IsActive).IsRequired().HasDefaultValue(true);

                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
                entity.HasIndex(e => e.InvoiceDate);
                entity.HasIndex(e => e.Status);

                entity.HasOne(e => e.Supplier)
                      .WithMany(s => s.Invoices)
                      .HasForeignKey(e => e.SupplierId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Payment Configuration
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ReceiptNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Amount).IsRequired().HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.PaymentDate).IsRequired();
                entity.Property(e => e.Method).IsRequired().HasDefaultValue(PaymentMethod.Cash);
                entity.Property(e => e.Status).IsRequired().HasDefaultValue(PaymentStatus.FullPayment);

                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.AttachmentPath).HasMaxLength(500);
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.IsActive).IsRequired().HasDefaultValue(true);

                entity.HasIndex(e => e.ReceiptNumber).IsUnique();
                entity.HasIndex(e => e.PaymentDate);

                entity.HasOne(e => e.Invoice)
                      .WithMany(i => i.Payments)
                      .HasForeignKey(e => e.InvoiceId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Seed Data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed default suppliers
            modelBuilder.Entity<Supplier>().HasData(
                new Supplier
                {
                    Id = 1,
                    Name = "مورد تجريبي",
                    ContactPerson = "أحمد محمد",
                    Phone = "01234567890",
                    Email = "<EMAIL>",
                    Address = "القاهرة، مصر",
                    Notes = "مورد تجريبي للاختبار",
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Local),
                    IsActive = true
                }
            );
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is Supplier || e.Entity is Invoice || e.Entity is Payment);

            foreach (var entry in entries)
            {
                if (entry.State == EntityState.Modified)
                {
                    if (entry.Entity is Supplier supplier)
                        supplier.UpdatedDate = DateTime.Now;
                    else if (entry.Entity is Invoice invoice)
                        invoice.UpdatedDate = DateTime.Now;
                    else if (entry.Entity is Payment payment)
                        payment.UpdatedDate = DateTime.Now;
                }
            }
        }
    }
}
